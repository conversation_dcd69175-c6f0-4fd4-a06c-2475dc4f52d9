#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import rclpy
from rclpy.node import Node
from rclpy.action import ActionServer
from rclpy.executors import MultiThreadedExecutor
from rclpy.callback_groups import ReentrantCallbackGroup
from rclpy.qos import QoSProfile, QoSReliabilityPolicy, QoSHistoryPolicy
import tf2_ros
from .Rosmaster_Lib import Rosmaster
from wy_actions.action import MainTask
from std_msgs.msg import Int32
from geometry_msgs.msg import Twist
from user_msgs.msg import BoundingBoxes
# from my_servo_control.msg import ServoCommand
import time
from .Twolunzi_driver import yahboomcar_driver

class MainTaskServer(Node):
    def __init__(self):
        Node.__init__(self, 'red_task_server')

        # 创建回调组以支持并发
        self.callback_group = ReentrantCallbackGroup()

        # 创建动作服务器
        self._action_server = ActionServer(
            self,
            MainTask,
            'red_task',
            self.execute_callback,
            callback_group=self.callback_group
        )

        """
        Parameters:
        self.target_color       :目标颜色
        self.target_placement   :目标放置区
        self.target_balls       :目标球（所有应该抓的球）
        self.zhuadaole          :抓取到位标志位
        self.task_time          :暂未用到
        self.see_color          :识别到的颜色
        self.vel_flag           :速度启动标志位
        self.vel_cmd            :速度消息初始化
        self.position_put       :放置位置到位标志位
        """

        """
        类别说明（与模型文件一致）
        Black_Ball:             0
        Blue_Ball:              1
        Blue_Placement_Zone:    2
        Blue_Start_Zone:        3
        Cross_Mark:             4
        Red_Ball:               5
        Red_Placement_Zone:     6
        Red_Start_Zone:         7
        Yellow_Ball:            8

        """

        self.target_color = "5.0"  # 红球
        self.target_placement = "6.0"  # 红色放置区
        self.target_balls = ['1.0','0.0','5.0','8.0']  # 蓝球、黑球、红球、黄球
        self.see_color = None
        self.detected = False

        self.zhuadaole = False
        self.vel_flag = False
        self.position_put = False
        self.slow_start_flag = False # 初始化速度阈值检测标志位
        self.cross_flag = True # 中心十字标志位、
        self.frame = True # 一帧标志位

        self.target_x = 0
        self.target_y = 0
        self.max_area_placement = 0
        self.max_box_placement = None
        self.max_box = None
        self.Collection_box = None
        self.Collect_ball_types = set()  # 用于存储框内球的种类
        self.max_area = 0
        self.image_height = 640
        self.image_width = 640

        self.go_num = 0 # 冲刺计数器
        self.det_num = 0 # 防误检测计数器
        self.lost_time = 0  # 重置目标物体丢失计时器
        self.slow_start_time = 0  # 初始化速度阈值检测计时器

        self.vel_cmd = Twist()
        self.vel_pub = self.create_publisher(Twist, "/cmd_vel", 10)
        # self.servo_pub = self.create_publisher(ServoCommand, 'servo_command', 10)
        # self.servo_msg = ServoCommand()

        # 配置QoS策略以匹配YOLO发布者
        qos_profile = QoSProfile(
            reliability=QoSReliabilityPolicy.BEST_EFFORT,
            history=QoSHistoryPolicy.KEEP_LAST,
            depth=10
        )

        # 订阅传感器数据
        self.yolo_sub = self.create_subscription(
            BoundingBoxes, '/yolo/detection', self.yolo_callback, qos_profile)
        self.sbus_sub = self.create_subscription(
            Int32, '/pub_sbus', self.sbus_callback, 10)
    
    def sbus_callback(self,msg):
        sbus_state = msg.data
        if sbus_state == 192:
            self.vel_flag = 0
        else:
            pass

    # 视觉
    def reset_detection_states(self):
        """
        重置目标检测初始值
        """
        self.detected = False
        self.max_area_placement = 0
        # self.max_box_placement = None # 虽然不知道为什么但是去掉就好用了
        self.max_area = 0
        self.max_box = None
        self.Collect_ball_types = set()
    
    def is_in_placement(self, box):
        """
        判断目标是否在放置区内
        """
        return (self.max_box_placement.xmin < (box.xmin + box.xmax) // 2 < self.max_box_placement.xmax and
                self.max_box_placement.ymin < (box.ymin + box.ymax) // 2 < self.max_box_placement.ymax)

    def slow_start_move(self):
        """
        速度低于阈值，持续时间过长，触发前进动作
        """
        self.vel_cmd.linear.x = 0.1
        self.vel_cmd.angular.z = 0.0
        self.vel_pub.publish(self.vel_cmd)
        time.sleep(2)
        self.vel_cmd.linear.x = -0.1
        self.vel_cmd.angular.z = 0.0
        self.vel_pub.publish(self.vel_cmd)
        time.sleep(1)
        self.vel_cmd.linear.x = 0.0
        self.vel_cmd.angular.z = 0.0
        self.vel_pub.publish(self.vel_cmd)      

    def slow_start_check(self):
        """
        低速检测（防止卡着识别不到球不动）
        """
        if abs(self.vel_cmd.linear.x) < 0.01:
            if self.slow_start_time == 0:
                self.slow_start_time = time.time()  # 记录开始慢速的时间
            elif time.time() - self.slow_start_time > 10 and not self.slow_start_flag :
                self.slow_start_flag = True
                print("速度低于阈值，持续时间过长，触发前进动作")
                self.slow_start_move()

        else:
            # 如果速度正常，则重置慢速检测计时器
            self.slow_start_time = 0
            self.slow_start_flag = False

    def target_loss_check(self):
        """
        目标丢失检测(超过5s开始自转寻找目标,超过13s更换目标)
        """
        if self.lost_time and (time.time() - self.lost_time) > 5 :  # 未检测到目标超过5s开始旋转/为了让小车在抓到球和放置后立刻旋转所加的标志位
            # print("找不到目标，开始旋转")
            self.vel_cmd.linear.x = 0.0  # 停止线性运动
            self.vel_cmd.angular.z = -0.015  # 固定角速度旋转

            if (time.time() - self.lost_time) > 13:
                print("更换目标了")
                self.lost_time = 0
                # feedback在execute_callback中处理

    def In_frame_detection_check(self,data):
        """
        对框内框住的物体进行检查(一帧);还需要完善
        """
        # if not self.frame:
        #     return
        for box in data.bounding_boxes:
            if box.label in self.target_balls:
                if (200 < box.xmin and  # xmin230
                    450 > box.xmax and  # xmax442
                    340 < box.ymin and  # ymin355
                    490 > box.ymax):    # ymax477
                    self.Collect_ball_types.add(box.label)
        # self.frame = False

    def handle_normal_detection(self):
        """
        正常情况下目标位置及速度处理
        """
        if self.det_num >= 3: 
            if self.max_box is not None:
                self.see_color = self.target_color
                self.target_x = (self.max_box.xmin + self.max_box.xmax) // 2
                self.target_y = (self.max_box.ymin + self.max_box.ymax) // 2 

                if self.see_color in ["2.0","7.0"]: # 放置区宽泛条件
                    # 最终速度
                    self.vel_cmd.linear.x = (self.image_height / 2 -10 - self.target_y) * 0.0040 
                    self.vel_cmd.angular.z = (self.image_width / 2 - self.target_x) * 0.00012
                else:
                    # 最终速度
                    self.vel_cmd.linear.x = (self.image_height / 2 + 100 - self.target_y) * 0.0025 #  +90 y在框中心
                    self.vel_cmd.angular.z = (self.image_width / 2 + 20 - self.target_x) * 0.00012 # +20 x在框中心

                print("vel_cmd.linear.x:", self.vel_cmd.linear.x,"vel_cmd.angular.z:", self.vel_cmd.angular.z)
                # print (self.see_color,self.target_x,self.target_y)  # 调试用

            else:
                # 未识别到时平滑减速
                self.vel_cmd.linear.x *= 0.9
                self.vel_cmd.angular.z *= 0.9
        else:
            # 如果没有连续检测到目标并且没有准备旋转，平滑减速
            self.vel_cmd.linear.x *= 0.9
            self.vel_cmd.angular.z *= 0.9     
        # print(self.vel_cmd.linear.x)

    # 目标检测回调函数
    def yolo_callback(self, data):
        """
        图像处理及速度发布
        """
        if not self.vel_flag:
            return
        # 重置
        self.reset_detection_states()
        
        for box in data.bounding_boxes:
            # 记录放置区信息(有bug)
            if box.label == self.target_placement:
                area_placement = (box.xmax - box.xmin) * (box.ymax - box.ymin)
                if area_placement > self.max_area_placement:
                    self.max_area_placement = area_placement
                    self.max_box_placement = box
                    # print(self.max_box_placement)

            # 球
            if box.label == self.target_color and box.label not in ["2.0", "7.0"]:
                # 当同类有多个目标时进行筛选
                area = (box.xmax - box.xmin) * (box.ymax - box.ymin)
                # print(self.max_box_placement)
                if area > self.max_area:
                    if self.max_box_placement is None or not self.is_in_placement(box): # 防止放置区内球干扰
                        self.max_area = area
                        self.max_box = box
                        self.detected = True
                        # print(self.max_area)

            # 放置区
            if box.label == self.target_color and (box.label == "2.0" or box.label == "7.0"):
                area = (box.xmax - box.xmin) * (box.ymax - box.ymin)
                if area > self.max_area:
                        self.max_area = area
                        self.max_box = box
                        self.detected = True

        if self.detected:
            self.det_num += 1
            self.lost_time = 0
        else:
            self.det_num = 0  # 如果没有检测到目标，重置计数器
            if self.lost_time == 0:
                self.lost_time = time.time()  # 记录第一次丢失的时间

        # 检查框内物体(防止抓多)
        self.In_frame_detection_check(data)
        # 目标丢失检测处理
        self.target_loss_check()
        # 正常目标位置及速度处理
        self.handle_normal_detection()
        # 低速检测处理
        self.slow_start_check()
        # 发布速度消息
        self.vel_pub.publish(self.vel_cmd)
        # print("种类：", list(self.Collect_ball_types))

    # 控制机器人的前进
    def move_robot(self):
        self.vel_cmd.linear.x = 0.30
        self.vel_cmd.angular.z = 0.0
        self.vel_pub.publish(self.vel_cmd)

    def move_robot_back(self):
        self.vel_cmd.linear.x = -1.3
        self.vel_cmd.angular.z = 0.0
        self.vel_pub.publish(self.vel_cmd)

    def rotate(self):
        """
        控制机器人旋转直到检测到目标颜色。
        """
        time.sleep(0.3)
        self.move_robot_back()
        time.sleep(3.0)
        self.stop_robot()  # 停止机器人
        time.sleep(0.3)
        print("开始旋转寻找目标颜色")
        twist = Twist()
        twist.linear.x = 0.0  # 后退的线速度（可根据需求调整）
        twist.angular.z = -0.015  # 角速度，负值表示逆时针旋转
        start_time = time.time()  # 获取开始旋转的时间
        while self.see_color != self.target_color and rclpy.ok():
            current_time = time.time()  # 获取当前时间
            self.vel_pub.publish(twist)  # 发布速度指令
            # self.get_logger().info(f"当前颜色: {self.see_color}, 目标颜色: {self.target_color}")
            if current_time - start_time >= 12:  # 如果旋转时间超过10s
                print("换目标啦来自rotate")
                current_time = 0
                start_time = 0
                # 这里需要在execute_callback中处理feedback
                break
            time.sleep(0.1)  # 控制循环频率
        current_time = 0
        start_time = 0
        # 停止机器人
        twist.linear.x = 0.0
        twist.angular.z = 0.0
        self.vel_pub.publish(twist)
        # rospy.loginfo("已找到目标颜色，停止旋转")

    # 控制机器人停止
    def stop_robot(self):
        self.vel_cmd.linear.x = 0.0
        self.vel_cmd.angular.z = 0.0
        self.vel_pub.publish(self.vel_cmd)

    def go(self):
        """
        冲刺冲散球
        """
        if self.go_num == 0:
            print("冲刺！！！！！！！！！！！")
            twist = Twist()
            twist.linear.x = 1.0 # 线速度
            self.vel_pub.publish(twist)  
            time.sleep(1.6)
            twist.linear.x = -0.7  # 线速度
            self.vel_pub.publish(twist)
            time.sleep(0.5)
            twist.linear.x = 0.0  # 线速度
            self.vel_pub.publish(twist)  
            self.go_num += 1


class Total(MainTaskServer, yahboomcar_driver):
    def __init__(self):
        yahboomcar_driver.__init__(self)
        MainTaskServer.__init__(self)

    # 执行抓取操作
    def pick(self):
        try:
            self.set_servo_angle(1,10)
        except Exception as e:
            self.get_logger().error(f"Error in pick operation: {e}")

    # 执行放置操作
    def put(self):
        try:
            self.set_servo_angle(1,140)
        except Exception as e:
            self.get_logger().error(f"Error in put operation: {e}")

    def execute_callback(self, goal_handle):
        """ROS2动作服务器回调函数"""
        self.get_logger().info('执行红色任务...')

        goal = goal_handle.request
        feedback_msg = MainTask.Feedback()
        result = MainTask.Result()

        self.go()
        self.vel_flag = True
        self.cross_flag = True
        self.target_color = goal.target_class

        if self.target_color == 'stand':
            self.put()
            self.zhuadaole = False
            self.position_put = False
            result.success = True
            result.id = goal.id
            result.result_message = "Red stand operation completed"
            goal_handle.succeed()
            return result

        self.rotate()
        rate = self.create_rate(10)
        while rclpy.ok():
            # print(self.image_width / 2,self.target_x,self.image_height / 2, self.target_y)
            if goal_handle.is_cancel_requested:
                self.get_logger().info("Goal canceled!")
                result.success = False
                result.id = goal.id
                result.result_message = "Goal was canceled"
                goal_handle.canceled()
                return result
            if self.see_color in ["2.0","7.0"]:
                if ( self.image_width / 2  -15 <= self.target_x <=  self.image_width / 2 + 15) and \
                   ( self.image_height / 2  -10 - 25 < self.target_y < self.image_height / 2 -10 + 25) : 
                    self.position_put = True
            else:
                if ( self.image_width / 2 + 20  - 10 <= self.target_x <=  self.image_width / 2 + 20 + 10) and \
                   ( self.image_height / 2 + 110 - 10 < self.target_y < self.image_height / 2 + 110 + 10) : 
                    self.position_put = True
                # pass
            # self.task_time += 1
            # # rospy.loginfo("Time: {}".format(self.task_time))
            # print(self.vel_cmd.linear.x,self.vel_cmd.angular.z)
            # # 如果任务时间超过100并且机器人处于停止状态，表示机器人已经到达目标位置
            # if self.task_time > 100 and abs(self.vel_cmd.linear.x) < 0.001 and abs(self.vel_cmd.angular.z) <= 0.1:
            #     self.position_put = True

            # 
            
            if self.target_color == '5.0' and self.position_put and self.cross_flag:
                self.vel_flag = False
                print("已到达中心处")
                # 完成当前目标后，中止该回调，等待新目标
                self._result.success = True
                self._as.set_succeeded(self._result)  # 设置任务为成功
                self.cross_flag = False
                return

            # 如果已经到达目标位置并且目标颜色匹配，开始执行抓取或放置操作
            if self.position_put and self.see_color == self.target_color:
                self.vel_flag = False
                # print("1:",self.target_color in self.target_balls)
                # print("2:",self.target_color)
                # print("3:",self.zhuadaole)
                self.Buzzer_set(20)
                self.get_logger().info("目标匹配，开始执行任务。")
                if self.target_color in self.target_balls and not self.zhuadaole: # 黑蓝红黄

                    self.get_logger().info("准备抓取目标。")
                    self.pick()
                    time.sleep(0.2)
                    self.zhuadaole = True  # 标记已经抓取
                    self.position_put = False

                    # 发送反馈
                    feedback_msg.feedback_message = 'grabbed'
                    goal_handle.publish_feedback(feedback_msg)

                    # 完成当前目标后，设置成功结果
                    result.success = True
                    result.id = goal.id
                    result.result_message = "Red target grabbed successfully"
                    goal_handle.succeed()
                    return result
                
                # 执行放置操作
                elif self.target_color == '7.0' and self.zhuadaole:
                    self.move_robot()  # 启动机器人移动
                    time.sleep(1)
                    self.stop_robot()  # 停止机器人
                    time.sleep(0.2)

                    self.get_logger().info("准备放置目标。")
                    self.put()
                    time.sleep(0.2)
                    self.zhuadaole = False
                    self.position_put = False

                    result.success = True
                    result.id = goal.id
                    result.result_message = "Red target placed successfully"
                    goal_handle.succeed()
                    return result

            # 发送移动反馈
            feedback_msg.feedback_message = 'moving'
            goal_handle.publish_feedback(feedback_msg)
            rate.sleep()  # 保持循环
def main(args=None):
    rclpy.init(args=args)

    executor = MultiThreadedExecutor()
    server = Total()

    try:
        executor.add_node(server)
        executor.spin()
    except KeyboardInterrupt:
        pass
    finally:
        server.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
