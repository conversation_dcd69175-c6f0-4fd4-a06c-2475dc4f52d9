[0.000000] (-) TimerEvent: {}
[0.000598] (-) JobUnselected: {'identifier': 'detection'}
[0.001041] (-) JobUnselected: {'identifier': 'my_servo_control'}
[0.001295] (-) JobUnselected: {'identifier': 'usb_cam'}
[0.001586] (-) JobUnselected: {'identifier': 'user_msgs'}
[0.001674] (-) JobUnselected: {'identifier': 'yahboomcar_bringup'}
[0.001840] (-) JobUnselected: {'identifier': 'yahboomcar_ctrl'}
[0.001912] (-) JobUnselected: {'identifier': 'yahboomcar_description'}
[0.001976] (wy_actions) JobQueued: {'identifier': 'wy_actions', 'dependencies': OrderedDict([('user_msgs', '/home/<USER>/znjy_ws/install/user_msgs')])}
[0.002051] (wy_actions) JobStarted: {'identifier': 'wy_actions'}
[0.036852] (wy_actions) JobProgress: {'identifier': 'wy_actions', 'progress': 'cmake'}
[0.038446] (wy_actions) JobProgress: {'identifier': 'wy_actions', 'progress': 'build'}
[0.039780] (wy_actions) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/znjy_ws/build/wy_actions', '--', '-j6', '-l6'], 'cwd': '/home/<USER>/znjy_ws/build/wy_actions', 'env': OrderedDict([('PYTHON_BASIC_REPL', '1'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'jetson'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/znjy_ws/install/user_msgs/lib:/home/<USER>/znjy_ws/install/wy_actions/lib:/home/<USER>/znjy_ws/install/usb_cam/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib:/usr/local/lib:/usr/local/cuda-12.6/lib64'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>/znjy_ws'), ('TERM_PROGRAM_VERSION', '1.99.3'), ('DESKTOP_SESSION', 'ubuntu'), ('NO_PROXY', 'localhost,*********/8,::1'), ('JETSON_L4T', '36.4.3'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('JETSON_MODEL', 'NVIDIA Jetson Orin Nano Engineering Reference Developer Kit Super'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '2546'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=64e768f683bf252bf1d6552b6872485d'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('https_proxy', 'http://127.0.0.1:7890/'), ('COLCON_PREFIX_PATH', '/home/<USER>/znjy_ws/install:/home/<USER>/ws00_helloworld/install:/home/<USER>/yahboom_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'ibus'), ('LOGNAME', 'jetson'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('JETSON_MODULE', 'NVIDIA Jetson Orin Nano (4GB ram)'), ('JETSON_SERIAL_NUMBER', '1423724364572'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'jetson'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/cuda-12.6/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/scripts/noConfigScripts'), ('SESSION_MANAGER', 'local/yahboom:@/tmp/.ICE-unix/2546,unix/yahboom:/tmp/.ICE-unix/2546'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/76f9407b_5329_43e8_b6ac_2da48569ce85'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-49243217e568589b.txt'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('JETSON_SOC', 'tegra234'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-675997bb9f.sock'), ('GNOME_TERMINAL_SERVICE', ':1.101'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '99'), ('AMENT_PREFIX_PATH', '/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('JETSON_CUDA_ARCH_BIN', '8.7'), ('PWD', '/home/<USER>/znjy_ws/build/wy_actions'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=64e768f683bf252bf1d6552b6872485d'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/znjy_ws/install/yahboomcar_description/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/yahboomcar_ctrl/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/yahboomcar_bringup/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages:/home/<USER>/znjy_ws/install/detection/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/user_msgs/local/lib/python3.10/dist-packages:/home/<USER>/znjy_ws/install/my_servo_control/lib/python3.10/site-packages:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py/lib/python3.10/site-packages:/home/<USER>/yahboom_ws/install/camera/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages:/usr/local/lib/python3.10/site-packages/:'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('JETSON_JETPACK', '6.2'), ('CMAKE_PREFIX_PATH', '/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble'), ('JETSON_P_NUMBER', 'p3767-0004')]), 'shell': False}
[0.099502] (-) TimerEvent: {}
[0.200090] (-) TimerEvent: {}
[0.300839] (-) TimerEvent: {}
[0.401730] (-) TimerEvent: {}
[0.502556] (-) TimerEvent: {}
[0.603309] (-) TimerEvent: {}
[0.627280] (wy_actions) StdoutLine: {'line': b'[  3%] Built target wy_actions__cpp\n'}
[0.682091] (wy_actions) StdoutLine: {'line': b'[ 12%] Built target wy_actions__rosidl_generator_c\n'}
[0.693642] (wy_actions) StdoutLine: {'line': b'[ 12%] Built target ament_cmake_python_copy_wy_actions\n'}
[0.703600] (-) TimerEvent: {}
[0.748489] (wy_actions) StdoutLine: {'line': b'[ 32%] Built target wy_actions__rosidl_typesupport_cpp\n'}
[0.748896] (wy_actions) StdoutLine: {'line': b'[ 32%] Built target wy_actions__rosidl_typesupport_introspection_c\n'}
[0.753050] (wy_actions) StdoutLine: {'line': b'[ 41%] Built target wy_actions__rosidl_typesupport_introspection_cpp\n'}
[0.801538] (wy_actions) StdoutLine: {'line': b'[ 51%] Built target wy_actions__rosidl_typesupport_c\n'}
[0.803674] (-) TimerEvent: {}
[0.837217] (wy_actions) StdoutLine: {'line': b'[ 61%] Built target wy_actions__rosidl_typesupport_fastrtps_c\n'}
[0.867686] (wy_actions) StdoutLine: {'line': b'[ 70%] Built target wy_actions__rosidl_typesupport_fastrtps_cpp\n'}
[0.903844] (-) TimerEvent: {}
[0.910570] (wy_actions) StdoutLine: {'line': b'[ 70%] Built target wy_actions\n'}
[0.958301] (wy_actions) StdoutLine: {'line': b'[ 74%] Built target wy_actions__py\n'}
[1.003990] (-) TimerEvent: {}
[1.021814] (wy_actions) StdoutLine: {'line': b'[ 80%] Built target wy_actions__rosidl_generator_py\n'}
[1.077525] (wy_actions) StdoutLine: {'line': b'[ 87%] Built target wy_actions__rosidl_typesupport_fastrtps_c__pyext\n'}
[1.077960] (wy_actions) StdoutLine: {'line': b'[ 93%] Built target wy_actions__rosidl_typesupport_introspection_c__pyext\n'}
[1.104134] (-) TimerEvent: {}
[1.135466] (wy_actions) StdoutLine: {'line': b'[100%] Built target wy_actions__rosidl_typesupport_c__pyext\n'}
[1.204279] (-) TimerEvent: {}
[1.304836] (-) TimerEvent: {}
[1.405421] (-) TimerEvent: {}
[1.506095] (-) TimerEvent: {}
[1.506603] (wy_actions) StdoutLine: {'line': b'running egg_info\n'}
[1.508750] (wy_actions) StdoutLine: {'line': b'writing wy_actions.egg-info/PKG-INFO\n'}
[1.509239] (wy_actions) StdoutLine: {'line': b'writing dependency_links to wy_actions.egg-info/dependency_links.txt\n'}
[1.509615] (wy_actions) StdoutLine: {'line': b'writing top-level names to wy_actions.egg-info/top_level.txt\n'}
[1.515809] (wy_actions) StdoutLine: {'line': b"reading manifest file 'wy_actions.egg-info/SOURCES.txt'\n"}
[1.516893] (wy_actions) StdoutLine: {'line': b"writing manifest file 'wy_actions.egg-info/SOURCES.txt'\n"}
[1.593563] (wy_actions) StdoutLine: {'line': b'[100%] Built target ament_cmake_python_build_wy_actions_egg\n'}
[1.606319] (-) TimerEvent: {}
[1.619713] (wy_actions) CommandEnded: {'returncode': 0}
[1.625423] (wy_actions) JobProgress: {'identifier': 'wy_actions', 'progress': 'install'}
[1.659069] (wy_actions) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/znjy_ws/build/wy_actions'], 'cwd': '/home/<USER>/znjy_ws/build/wy_actions', 'env': OrderedDict([('PYTHON_BASIC_REPL', '1'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'jetson'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/znjy_ws/install/user_msgs/lib:/home/<USER>/znjy_ws/install/wy_actions/lib:/home/<USER>/znjy_ws/install/usb_cam/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib:/usr/local/lib:/usr/local/cuda-12.6/lib64'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>/znjy_ws'), ('TERM_PROGRAM_VERSION', '1.99.3'), ('DESKTOP_SESSION', 'ubuntu'), ('NO_PROXY', 'localhost,*********/8,::1'), ('JETSON_L4T', '36.4.3'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('JETSON_MODEL', 'NVIDIA Jetson Orin Nano Engineering Reference Developer Kit Super'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '2546'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=64e768f683bf252bf1d6552b6872485d'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('https_proxy', 'http://127.0.0.1:7890/'), ('COLCON_PREFIX_PATH', '/home/<USER>/znjy_ws/install:/home/<USER>/ws00_helloworld/install:/home/<USER>/yahboom_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'ibus'), ('LOGNAME', 'jetson'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('JETSON_MODULE', 'NVIDIA Jetson Orin Nano (4GB ram)'), ('JETSON_SERIAL_NUMBER', '1423724364572'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'jetson'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/cuda-12.6/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/scripts/noConfigScripts'), ('SESSION_MANAGER', 'local/yahboom:@/tmp/.ICE-unix/2546,unix/yahboom:/tmp/.ICE-unix/2546'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/76f9407b_5329_43e8_b6ac_2da48569ce85'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-49243217e568589b.txt'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('JETSON_SOC', 'tegra234'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-675997bb9f.sock'), ('GNOME_TERMINAL_SERVICE', ':1.101'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '99'), ('AMENT_PREFIX_PATH', '/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('JETSON_CUDA_ARCH_BIN', '8.7'), ('PWD', '/home/<USER>/znjy_ws/build/wy_actions'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=64e768f683bf252bf1d6552b6872485d'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/znjy_ws/install/yahboomcar_description/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/yahboomcar_ctrl/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/yahboomcar_bringup/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages:/home/<USER>/znjy_ws/install/detection/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/user_msgs/local/lib/python3.10/dist-packages:/home/<USER>/znjy_ws/install/my_servo_control/lib/python3.10/site-packages:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py/lib/python3.10/site-packages:/home/<USER>/yahboom_ws/install/camera/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages:/usr/local/lib/python3.10/site-packages/:'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('JETSON_JETPACK', '6.2'), ('CMAKE_PREFIX_PATH', '/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble'), ('JETSON_P_NUMBER', 'p3767-0004')]), 'shell': False}
[1.675726] (wy_actions) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[1.681744] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/ament_index/resource_index/rosidl_interfaces/wy_actions\n'}
[1.682558] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions\n'}
[1.682840] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action\n'}
[1.682991] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail\n'}
[1.683136] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__struct.h\n'}
[1.683761] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__functions.c\n'}
[1.683973] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__functions.h\n'}
[1.684209] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__type_support.h\n'}
[1.684462] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/main_task.h\n'}
[1.684817] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg\n'}
[1.685008] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg/rosidl_generator_c__visibility_control.h\n'}
[1.685182] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/library_path.sh\n'}
[1.685346] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/library_path.dsv\n'}
[1.685590] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_generator_c.so\n'}
[1.686076] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions\n'}
[1.686333] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action\n'}
[1.686545] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail\n'}
[1.686807] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__rosidl_typesupport_fastrtps_c.h\n'}
[1.687018] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg\n'}
[1.687256] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg/rosidl_typesupport_fastrtps_c__visibility_control.h\n'}
[1.687463] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_fastrtps_c.so\n'}
[1.687773] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions\n'}
[1.688837] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action\n'}
[1.689098] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail\n'}
[1.689274] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__rosidl_typesupport_introspection_c.h\n'}
[1.689443] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__type_support.c\n'}
[1.689644] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg\n'}
[1.689845] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg/rosidl_typesupport_introspection_c__visibility_control.h\n'}
[1.690046] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_introspection_c.so\n'}
[1.690262] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_c.so\n'}
[1.690411] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions\n'}
[1.690531] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action\n'}
[1.690643] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail\n'}
[1.690755] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__traits.hpp\n'}
[1.691243] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__builder.hpp\n'}
[1.691377] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__struct.hpp\n'}
[1.691488] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__type_support.hpp\n'}
[1.692508] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/main_task.hpp\n'}
[1.692724] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg\n'}
[1.692916] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg/rosidl_generator_cpp__visibility_control.hpp\n'}
[1.693122] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions\n'}
[1.693267] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action\n'}
[1.693398] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail\n'}
[1.693521] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[1.693685] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/dds_fastrtps\n'}
[1.693829] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg\n'}
[1.693957] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h\n'}
[1.694082] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_fastrtps_cpp.so\n'}
[1.694209] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions\n'}
[1.694342] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action\n'}
[1.694474] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail\n'}
[1.694598] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__type_support.cpp\n'}
[1.695907] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__rosidl_typesupport_introspection_cpp.hpp\n'}
[1.696120] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_introspection_cpp.so\n'}
[1.696310] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_cpp.so\n'}
[1.696494] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/pythonpath.sh\n'}
[1.696648] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/pythonpath.dsv\n'}
[1.696847] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions-0.0.0-py3.10.egg-info\n'}
[1.697127] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions-0.0.0-py3.10.egg-info/dependency_links.txt\n'}
[1.697622] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions-0.0.0-py3.10.egg-info/top_level.txt\n'}
[1.697747] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions-0.0.0-py3.10.egg-info/SOURCES.txt\n'}
[1.697869] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions-0.0.0-py3.10.egg-info/PKG-INFO\n'}
[1.697988] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions\n'}
[1.698103] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/action\n'}
[1.698257] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/action/__init__.py\n'}
[1.698376] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/action/_main_task.py\n'}
[1.698494] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/action/_main_task_s.c\n'}
[1.698626] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/_wy_actions_s.ep.rosidl_typesupport_introspection_c.c\n'}
[1.698749] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/__init__.py\n'}
[1.698866] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_fastrtps_c.cpython-310-aarch64-linux-gnu.so\n'}
[1.698984] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/_wy_actions_s.ep.rosidl_typesupport_c.c\n'}
[1.699099] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/libwy_actions__rosidl_generator_py.so\n'}
[1.699246] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/_wy_actions_s.ep.rosidl_typesupport_fastrtps_c.c\n'}
[1.699368] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_c.cpython-310-aarch64-linux-gnu.so\n'}
[1.699486] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_introspection_c.cpython-310-aarch64-linux-gnu.so\n'}
[1.706448] (-) TimerEvent: {}
[1.790555] (wy_actions) StdoutLine: {'line': b"Listing '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions'...\n"}
[1.790937] (wy_actions) StdoutLine: {'line': b"Listing '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/action'...\n"}
[1.791082] (wy_actions) StdoutLine: {'line': b"Listing '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/drive'...\n"}
[1.791241] (wy_actions) StdoutLine: {'line': b"Compiling '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/drive/red_client.py'...\n"}
[1.791373] (wy_actions) StdoutLine: {'line': b"Compiling '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/drive/red_server.py'...\n"}
[1.806432] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_fastrtps_c.cpython-310-aarch64-linux-gnu.so\n'}
[1.806817] (-) TimerEvent: {}
[1.807196] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_introspection_c.cpython-310-aarch64-linux-gnu.so\n'}
[1.808783] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_c.cpython-310-aarch64-linux-gnu.so\n'}
[1.809672] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_generator_py.so\n'}
[1.809884] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/action/MainTask.idl\n'}
[1.810091] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/action/MainTask.action\n'}
[1.810262] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/ament_index/resource_index/package_run_dependencies/wy_actions\n'}
[1.810442] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/ament_index/resource_index/parent_prefix_path/wy_actions\n'}
[1.810579] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/ament_prefix_path.sh\n'}
[1.810814] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/ament_prefix_path.dsv\n'}
[1.810971] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/path.sh\n'}
[1.811270] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/path.dsv\n'}
[1.811427] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/local_setup.bash\n'}
[1.811550] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/local_setup.sh\n'}
[1.811693] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/local_setup.zsh\n'}
[1.811850] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/local_setup.dsv\n'}
[1.811973] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.dsv\n'}
[1.812175] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/ament_index/resource_index/packages/wy_actions\n'}
[1.815229] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_cExport.cmake\n'}
[1.815625] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_cExport-noconfig.cmake\n'}
[1.815786] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_typesupport_fastrtps_cExport.cmake\n'}
[1.815920] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_typesupport_fastrtps_cExport-noconfig.cmake\n'}
[1.817095] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_introspection_cExport.cmake\n'}
[1.817402] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_introspection_cExport-noconfig.cmake\n'}
[1.817950] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_cExport.cmake\n'}
[1.818187] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_cExport-noconfig.cmake\n'}
[1.818655] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_cppExport.cmake\n'}
[1.820070] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_typesupport_fastrtps_cppExport.cmake\n'}
[1.820302] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake\n'}
[1.820501] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_introspection_cppExport.cmake\n'}
[1.820691] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_introspection_cppExport-noconfig.cmake\n'}
[1.821371] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_cppExport.cmake\n'}
[1.821610] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_cppExport-noconfig.cmake\n'}
[1.822122] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_pyExport.cmake\n'}
[1.824083] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_pyExport-noconfig.cmake\n'}
[1.824538] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/rosidl_cmake-extras.cmake\n'}
[1.825456] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[1.828151] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[1.828564] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[1.829695] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/ament_cmake_export_targets-extras.cmake\n'}
[1.830033] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake\n'}
[1.830224] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake\n'}
[1.830366] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actionsConfig.cmake\n'}
[1.830498] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actionsConfig-version.cmake\n'}
[1.830627] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.xml\n'}
[1.833347] (wy_actions) CommandEnded: {'returncode': 0}
[1.899712] (wy_actions) JobEnded: {'identifier': 'wy_actions', 'rc': 0}
[1.901121] (-) EventReactorShutdown: {}
