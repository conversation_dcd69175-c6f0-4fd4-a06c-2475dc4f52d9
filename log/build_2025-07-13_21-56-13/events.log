[0.000000] (-) TimerEvent: {}
[0.001360] (-) JobUnselected: {'identifier': 'my_servo_control'}
[0.001832] (-) JobUnselected: {'identifier': 'usb_cam'}
[0.002179] (-) JobUnselected: {'identifier': 'user_msgs'}
[0.002283] (-) JobUnselected: {'identifier': 'wy_actions'}
[0.002443] (-) JobUnselected: {'identifier': 'yahboomcar_bringup'}
[0.002516] (-) JobUnselected: {'identifier': 'yahboomcar_ctrl'}
[0.002555] (-) JobUnselected: {'identifier': 'yahboomcar_description'}
[0.002620] (detection) JobQueued: {'identifier': 'detection', 'dependencies': OrderedDict([('user_msgs', '/home/<USER>/znjy_ws/install/user_msgs')])}
[0.002675] (detection) JobStarted: {'identifier': 'detection'}
[0.098566] (-) TimerEvent: {}
[0.199045] (-) TimerEvent: {}
[0.299697] (-) TimerEvent: {}
[0.400245] (-) TimerEvent: {}
[0.500797] (-) TimerEvent: {}
[0.601305] (-) TimerEvent: {}
[0.701899] (-) TimerEvent: {}
[0.802470] (-) TimerEvent: {}
[0.903025] (-) TimerEvent: {}
[1.003594] (-) TimerEvent: {}
[1.104095] (-) TimerEvent: {}
[1.204630] (-) TimerEvent: {}
[1.305148] (-) TimerEvent: {}
[1.405763] (-) TimerEvent: {}
[1.506348] (-) TimerEvent: {}
[1.606919] (-) TimerEvent: {}
[1.707522] (-) TimerEvent: {}
[1.808110] (-) TimerEvent: {}
[1.908713] (-) TimerEvent: {}
[2.009346] (-) TimerEvent: {}
[2.109901] (-) TimerEvent: {}
[2.209424] (detection) Command: {'cmd': ['/usr/bin/python3', '-W', 'ignore:setup.py install is deprecated', '-W', 'ignore:easy_install command is deprecated', 'setup.py', 'egg_info', '--egg-base', '../../build/detection', 'build', '--build-base', '/home/<USER>/znjy_ws/build/detection/build', 'install', '--record', '/home/<USER>/znjy_ws/build/detection/install.log', '--single-version-externally-managed', 'install_data'], 'cwd': '/home/<USER>/znjy_ws/src/detection', 'env': {'PYTHON_BASIC_REPL': '1', 'LESSOPEN': '| /usr/bin/lesspipe %s', 'HTTPS_PROXY': 'http://127.0.0.1:7890/', 'no_proxy': 'localhost,*********/8,::1', 'LANGUAGE': 'zh_CN:en_US:en', 'USER': 'jetson', 'LC_TIME': 'zh_CN.UTF-8', 'all_proxy': 'socks://127.0.0.1:7891/', 'XDG_SESSION_TYPE': 'x11', 'GIT_ASKPASS': '/usr/share/code/resources/app/extensions/git/dist/askpass.sh', 'SHLVL': '2', 'LD_LIBRARY_PATH': '/home/<USER>/znjy_ws/install/user_msgs/lib:/home/<USER>/znjy_ws/install/wy_actions/lib:/home/<USER>/znjy_ws/install/usb_cam/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib:/usr/local/lib:/usr/local/cuda-12.6/lib64', 'LESS': '-FX', 'HOME': '/home/<USER>', 'CHROME_DESKTOP': 'code.desktop', 'OLDPWD': '/home/<USER>/znjy_ws', 'TERM_PROGRAM_VERSION': '1.99.3', 'DESKTOP_SESSION': 'ubuntu', 'NO_PROXY': 'localhost,*********/8,::1', 'JETSON_L4T': '36.4.3', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'JETSON_MODEL': 'NVIDIA Jetson Orin Nano Engineering Reference Developer Kit Super', 'PAGER': 'cat', 'VSCODE_GIT_ASKPASS_MAIN': '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js', 'LC_MONETARY': 'zh_CN.UTF-8', 'VSCODE_GIT_ASKPASS_NODE': '/usr/share/code/code', 'PYDEVD_DISABLE_FILE_VALIDATION': '1', 'DBUS_STARTER_BUS_TYPE': 'session', 'SYSTEMD_EXEC_PID': '2546', 'BUNDLED_DEBUGPY_PATH': '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus,guid=64e768f683bf252bf1d6552b6872485d', 'COLORTERM': 'truecolor', 'IM_CONFIG_PHASE': '1', 'https_proxy': 'http://127.0.0.1:7890/', 'COLCON_PREFIX_PATH': '/home/<USER>/znjy_ws/install:/home/<USER>/ws00_helloworld/install:/home/<USER>/yahboom_ws/install', 'ROS_DISTRO': 'humble', 'GTK_IM_MODULE': 'ibus', 'LOGNAME': 'jetson', 'ALL_PROXY': 'socks://127.0.0.1:7891/', 'JETSON_MODULE': 'NVIDIA Jetson Orin Nano (4GB ram)', 'JETSON_SERIAL_NUMBER': '1423724364572', 'http_proxy': 'http://127.0.0.1:7890/', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'USERNAME': 'jetson', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', 'ROS_LOCALHOST_ONLY': '0', 'WINDOWPATH': '2', 'PATH': '/opt/ros/humble/bin:/usr/local/cuda-12.6/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/scripts/noConfigScripts', 'SESSION_MANAGER': 'local/yahboom:@/tmp/.ICE-unix/2546,unix/yahboom:/tmp/.ICE-unix/2546', 'PAPERSIZE': 'a4', 'XDG_MENU_PREFIX': 'gnome-', 'LC_ADDRESS': 'zh_CN.UTF-8', 'GNOME_TERMINAL_SCREEN': '/org/gnome/Terminal/screen/76f9407b_5329_43e8_b6ac_2da48569ce85', 'XDG_RUNTIME_DIR': '/run/user/1000', 'GDK_BACKEND': 'x11', 'DISPLAY': ':0', 'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS': '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-49243217e568589b.txt', 'LANG': 'zh_CN.UTF-8', 'XDG_CURRENT_DESKTOP': 'Unity', 'LC_TELEPHONE': 'zh_CN.UTF-8', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/gdm/Xauthority', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:', 'JETSON_SOC': 'tegra234', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-675997bb9f.sock', 'GNOME_TERMINAL_SERVICE': ':1.101', 'TERM_PROGRAM': 'vscode', 'SSH_AGENT_LAUNCHER': 'gnome-keyring', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'ROS_DOMAIN_ID': '99', 'AMENT_PREFIX_PATH': '/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'SHELL': '/bin/bash', 'LC_NAME': 'zh_CN.UTF-8', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'LC_MEASUREMENT': 'zh_CN.UTF-8', 'GPG_AGENT_INFO': '/run/user/1000/gnupg/S.gpg-agent:0:1', 'LC_IDENTIFICATION': 'zh_CN.UTF-8', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'GIT_PAGER': 'cat', 'QT_IM_MODULE': 'ibus', 'JETSON_CUDA_ARCH_BIN': '8.7', 'PWD': '/home/<USER>/znjy_ws/build/detection', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'DBUS_STARTER_ADDRESS': 'unix:path=/run/user/1000/bus,guid=64e768f683bf252bf1d6552b6872485d', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/znjy_ws/build/detection/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/znjy_ws/install/detection/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/yahboomcar_description/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/yahboomcar_ctrl/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/yahboomcar_bringup/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages:/home/<USER>/znjy_ws/install/detection/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/user_msgs/local/lib/python3.10/dist-packages:/home/<USER>/znjy_ws/install/my_servo_control/lib/python3.10/site-packages:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py/lib/python3.10/site-packages:/home/<USER>/yahboom_ws/install/camera/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages:/usr/local/lib/python3.10/site-packages/:', 'HTTP_PROXY': 'http://127.0.0.1:7890/', 'LC_NUMERIC': 'zh_CN.UTF-8', 'LC_PAPER': 'zh_CN.UTF-8', 'COLCON': '1', 'VTE_VERSION': '6800', 'JETSON_JETPACK': '6.2', 'CMAKE_PREFIX_PATH': '/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp', 'JETSON_P_NUMBER': 'p3767-0004'}, 'shell': False}
[2.212281] (-) TimerEvent: {}
[2.312662] (-) TimerEvent: {}
[2.413284] (-) TimerEvent: {}
[2.513865] (-) TimerEvent: {}
[2.614475] (-) TimerEvent: {}
[2.715018] (-) TimerEvent: {}
[2.794662] (detection) StdoutLine: {'line': b'running egg_info\n'}
[2.796314] (detection) StdoutLine: {'line': b'writing ../../build/detection/detection.egg-info/PKG-INFO\n'}
[2.800383] (detection) StdoutLine: {'line': b'writing dependency_links to ../../build/detection/detection.egg-info/dependency_links.txt\n'}
[2.800794] (detection) StdoutLine: {'line': b'writing entry points to ../../build/detection/detection.egg-info/entry_points.txt\n'}
[2.800987] (detection) StdoutLine: {'line': b'writing requirements to ../../build/detection/detection.egg-info/requires.txt\n'}
[2.801122] (detection) StdoutLine: {'line': b'writing top-level names to ../../build/detection/detection.egg-info/top_level.txt\n'}
[2.803825] (detection) StdoutLine: {'line': b"reading manifest file '../../build/detection/detection.egg-info/SOURCES.txt'\n"}
[2.805038] (detection) StdoutLine: {'line': b"adding license file 'LICENSE'\n"}
[2.810775] (detection) StdoutLine: {'line': b"writing manifest file '../../build/detection/detection.egg-info/SOURCES.txt'\n"}
[2.811314] (detection) StdoutLine: {'line': b'running build\n'}
[2.811481] (detection) StdoutLine: {'line': b'running build_py\n'}
[2.812588] (detection) StdoutLine: {'line': b'copying detection/yolo_ros.py -> /home/<USER>/znjy_ws/build/detection/build/lib/detection\n'}
[2.813465] (detection) StdoutLine: {'line': b'copying detection/params.py -> /home/<USER>/znjy_ws/build/detection/build/lib/detection\n'}
[2.815134] (detection) StdoutLine: {'line': b'running install\n'}
[2.815340] (-) TimerEvent: {}
[2.816075] (detection) StdoutLine: {'line': b'running install_lib\n'}
[2.818664] (detection) StdoutLine: {'line': b'copying /home/<USER>/znjy_ws/build/detection/build/lib/detection/yolo_ros.py -> /home/<USER>/znjy_ws/install/detection/lib/python3.10/site-packages/detection\n'}
[2.819217] (detection) StdoutLine: {'line': b'copying /home/<USER>/znjy_ws/build/detection/build/lib/detection/params.py -> /home/<USER>/znjy_ws/install/detection/lib/python3.10/site-packages/detection\n'}
[2.820731] (detection) StdoutLine: {'line': b'byte-compiling /home/<USER>/znjy_ws/install/detection/lib/python3.10/site-packages/detection/yolo_ros.py to yolo_ros.cpython-310.pyc\n'}
[2.830714] (detection) StdoutLine: {'line': b'byte-compiling /home/<USER>/znjy_ws/install/detection/lib/python3.10/site-packages/detection/params.py to params.cpython-310.pyc\n'}
[2.832176] (detection) StdoutLine: {'line': b'running install_data\n'}
[2.832513] (detection) StdoutLine: {'line': b'running install_egg_info\n'}
[2.836532] (detection) StdoutLine: {'line': b"removing '/home/<USER>/znjy_ws/install/detection/lib/python3.10/site-packages/detection-0.0.0-py3.10.egg-info' (and everything under it)\n"}
[2.837141] (detection) StdoutLine: {'line': b'Copying ../../build/detection/detection.egg-info to /home/<USER>/znjy_ws/install/detection/lib/python3.10/site-packages/detection-0.0.0-py3.10.egg-info\n'}
[2.839271] (detection) StdoutLine: {'line': b'running install_scripts\n'}
[2.882856] (detection) StdoutLine: {'line': b'Installing image_node script to /home/<USER>/znjy_ws/install/detection/lib/detection\n'}
[2.883698] (detection) StdoutLine: {'line': b'Installing params script to /home/<USER>/znjy_ws/install/detection/lib/detection\n'}
[2.884293] (detection) StdoutLine: {'line': b'Installing yolo_ros script to /home/<USER>/znjy_ws/install/detection/lib/detection\n'}
[2.884715] (detection) StdoutLine: {'line': b"writing list of installed files to '/home/<USER>/znjy_ws/build/detection/install.log'\n"}
[2.915559] (-) TimerEvent: {}
[2.953050] (detection) CommandEnded: {'returncode': 0}
[2.994461] (detection) JobEnded: {'identifier': 'detection', 'rc': 0}
[2.996276] (-) EventReactorShutdown: {}
