[0.000000] (-) TimerEvent: {}
[0.001701] (-) JobUnselected: {'identifier': 'detection'}
[0.001908] (-) JobUnselected: {'identifier': 'my_servo_control'}
[0.002003] (-) JobUnselected: {'identifier': 'usb_cam'}
[0.002054] (-) JobUnselected: {'identifier': 'user_msgs'}
[0.002154] (-) JobUnselected: {'identifier': 'yahboomcar_bringup'}
[0.002194] (-) JobUnselected: {'identifier': 'yahboomcar_ctrl'}
[0.002231] (-) JobUnselected: {'identifier': 'yahboomcar_description'}
[0.002275] (wy_actions) JobQueued: {'identifier': 'wy_actions', 'dependencies': OrderedDict([('user_msgs', '/home/<USER>/znjy_ws/install/user_msgs')])}
[0.002353] (wy_actions) JobStarted: {'identifier': 'wy_actions'}
[0.042881] (wy_actions) JobProgress: {'identifier': 'wy_actions', 'progress': 'cmake'}
[0.045180] (wy_actions) JobProgress: {'identifier': 'wy_actions', 'progress': 'build'}
[0.046601] (wy_actions) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/znjy_ws/build/wy_actions', '--', '-j6', '-l6'], 'cwd': '/home/<USER>/znjy_ws/build/wy_actions', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'jetson'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/znjy_ws/install/user_msgs/lib:/home/<USER>/znjy_ws/install/wy_actions/lib:/home/<USER>/znjy_ws/install/usb_cam/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib:/usr/local/lib:/usr/local/cuda-12.6/lib64'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>/znjy_ws'), ('TERM_PROGRAM_VERSION', '1.99.3'), ('DESKTOP_SESSION', 'ubuntu'), ('NO_PROXY', 'localhost,*********/8,::1'), ('JETSON_L4T', '36.4.3'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('JETSON_MODEL', 'NVIDIA Jetson Orin Nano Engineering Reference Developer Kit Super'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '2446'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=40e36d718764c8131ab2b853688b1f07'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('https_proxy', 'http://127.0.0.1:7890/'), ('COLCON_PREFIX_PATH', '/home/<USER>/znjy_ws/install:/home/<USER>/ws00_helloworld/install:/home/<USER>/yahboom_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'ibus'), ('LOGNAME', 'jetson'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('JETSON_MODULE', 'NVIDIA Jetson Orin Nano (4GB ram)'), ('JETSON_SERIAL_NUMBER', '1423724364572'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'jetson'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/cuda-12.6/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/scripts/noConfigScripts'), ('SESSION_MANAGER', 'local/yahboom:@/tmp/.ICE-unix/2446,unix/yahboom:/tmp/.ICE-unix/2446'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/ac083d08_ceca_4372_adfa_69ba8249bdd6'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-49243217e568589b.txt'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('JETSON_SOC', 'tegra234'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-675997bb9f.sock'), ('GNOME_TERMINAL_SERVICE', ':1.101'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '0'), ('AMENT_PREFIX_PATH', '/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('JETSON_CUDA_ARCH_BIN', '8.7'), ('PWD', '/home/<USER>/znjy_ws/build/wy_actions'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=40e36d718764c8131ab2b853688b1f07'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/znjy_ws/install/yahboomcar_description/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/yahboomcar_ctrl/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/yahboomcar_bringup/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages:/home/<USER>/znjy_ws/install/detection/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/user_msgs/local/lib/python3.10/dist-packages:/home/<USER>/znjy_ws/install/my_servo_control/lib/python3.10/site-packages:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py/lib/python3.10/site-packages:/home/<USER>/yahboom_ws/install/camera/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages:/usr/local/lib/python3.10/site-packages/:'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('JETSON_JETPACK', '6.2'), ('CMAKE_PREFIX_PATH', '/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble'), ('JETSON_P_NUMBER', 'p3767-0004')]), 'shell': False}
[0.098386] (-) TimerEvent: {}
[0.199142] (-) TimerEvent: {}
[0.299890] (-) TimerEvent: {}
[0.400598] (-) TimerEvent: {}
[0.501412] (-) TimerEvent: {}
[0.574228] (wy_actions) StdoutLine: {'line': b'[  3%] Built target wy_actions__cpp\n'}
[0.607428] (-) TimerEvent: {}
[0.633471] (wy_actions) StdoutLine: {'line': b'[  3%] Built target ament_cmake_python_copy_wy_actions\n'}
[0.671193] (wy_actions) StdoutLine: {'line': b'[ 12%] Built target wy_actions__rosidl_generator_c\n'}
[0.683589] (wy_actions) StdoutLine: {'line': b'[ 22%] Built target wy_actions__rosidl_typesupport_introspection_cpp\n'}
[0.689286] (wy_actions) StdoutLine: {'line': b'[ 32%] Built target wy_actions__rosidl_typesupport_cpp\n'}
[0.698524] (wy_actions) StdoutLine: {'line': b'[ 41%] Built target wy_actions__rosidl_typesupport_fastrtps_cpp\n'}
[0.707646] (-) TimerEvent: {}
[0.734824] (wy_actions) StdoutLine: {'line': b'[ 61%] Built target wy_actions__rosidl_typesupport_fastrtps_c\n'}
[0.735318] (wy_actions) StdoutLine: {'line': b'[ 61%] Built target wy_actions__rosidl_typesupport_introspection_c\n'}
[0.740801] (wy_actions) StdoutLine: {'line': b'[ 70%] Built target wy_actions__rosidl_typesupport_c\n'}
[0.783432] (wy_actions) StdoutLine: {'line': b'[ 70%] Built target wy_actions\n'}
[0.808509] (-) TimerEvent: {}
[0.830175] (wy_actions) StdoutLine: {'line': b'[ 74%] Built target wy_actions__py\n'}
[0.892720] (wy_actions) StdoutLine: {'line': b'[ 80%] Built target wy_actions__rosidl_generator_py\n'}
[0.908845] (-) TimerEvent: {}
[0.947076] (wy_actions) StdoutLine: {'line': b'[ 93%] Built target wy_actions__rosidl_typesupport_c__pyext\n'}
[0.947679] (wy_actions) StdoutLine: {'line': b'[ 93%] Built target wy_actions__rosidl_typesupport_fastrtps_c__pyext\n'}
[0.954574] (wy_actions) StdoutLine: {'line': b'[100%] Built target wy_actions__rosidl_typesupport_introspection_c__pyext\n'}
[1.009033] (-) TimerEvent: {}
[1.109660] (-) TimerEvent: {}
[1.210837] (-) TimerEvent: {}
[1.285092] (wy_actions) StdoutLine: {'line': b'running egg_info\n'}
[1.287549] (wy_actions) StdoutLine: {'line': b'writing wy_actions.egg-info/PKG-INFO\n'}
[1.288202] (wy_actions) StdoutLine: {'line': b'writing dependency_links to wy_actions.egg-info/dependency_links.txt\n'}
[1.288675] (wy_actions) StdoutLine: {'line': b'writing top-level names to wy_actions.egg-info/top_level.txt\n'}
[1.295254] (wy_actions) StdoutLine: {'line': b"reading manifest file 'wy_actions.egg-info/SOURCES.txt'\n"}
[1.296325] (wy_actions) StdoutLine: {'line': b"writing manifest file 'wy_actions.egg-info/SOURCES.txt'\n"}
[1.310829] (-) TimerEvent: {}
