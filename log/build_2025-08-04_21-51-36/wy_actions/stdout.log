[  3%] Built target wy_actions__cpp
[  3%] Built target ament_cmake_python_copy_wy_actions
[ 12%] Built target wy_actions__rosidl_generator_c
[ 22%] Built target wy_actions__rosidl_typesupport_introspection_cpp
[ 32%] Built target wy_actions__rosidl_typesupport_cpp
[ 41%] Built target wy_actions__rosidl_typesupport_fastrtps_cpp
[ 61%] Built target wy_actions__rosidl_typesupport_fastrtps_c
[ 61%] Built target wy_actions__rosidl_typesupport_introspection_c
[ 70%] Built target wy_actions__rosidl_typesupport_c
[ 70%] Built target wy_actions
[ 74%] Built target wy_actions__py
[ 80%] Built target wy_actions__rosidl_generator_py
[ 93%] Built target wy_actions__rosidl_typesupport_c__pyext
[ 93%] Built target wy_actions__rosidl_typesupport_fastrtps_c__pyext
[100%] Built target wy_actions__rosidl_typesupport_introspection_c__pyext
running egg_info
writing wy_actions.egg-info/PKG-INFO
writing dependency_links to wy_actions.egg-info/dependency_links.txt
writing top-level names to wy_actions.egg-info/top_level.txt
reading manifest file 'wy_actions.egg-info/SOURCES.txt'
writing manifest file 'wy_actions.egg-info/SOURCES.txt'
[100%] Built target ament_cmake_python_build_wy_actions_egg
-- Install configuration: ""
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/ament_index/resource_index/rosidl_interfaces/wy_actions
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__struct.h
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__functions.c
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__functions.h
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__type_support.h
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/main_task.h
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg/rosidl_generator_c__visibility_control.h
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/library_path.sh
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/library_path.dsv
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_generator_c.so
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__rosidl_typesupport_fastrtps_c.h
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg/rosidl_typesupport_fastrtps_c__visibility_control.h
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_fastrtps_c.so
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__rosidl_typesupport_introspection_c.h
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__type_support.c
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg/rosidl_typesupport_introspection_c__visibility_control.h
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_introspection_c.so
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_c.so
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__traits.hpp
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__builder.hpp
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__struct.hpp
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__type_support.hpp
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/main_task.hpp
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg/rosidl_generator_cpp__visibility_control.hpp
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__rosidl_typesupport_fastrtps_cpp.hpp
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/dds_fastrtps
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_fastrtps_cpp.so
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__type_support.cpp
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__rosidl_typesupport_introspection_cpp.hpp
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_introspection_cpp.so
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_cpp.so
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/pythonpath.sh
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/pythonpath.dsv
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions-0.0.0-py3.10.egg-info
-- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions-0.0.0-py3.10.egg-info/dependency_links.txt
-- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions-0.0.0-py3.10.egg-info/top_level.txt
-- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions-0.0.0-py3.10.egg-info/SOURCES.txt
-- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions-0.0.0-py3.10.egg-info/PKG-INFO
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/action
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/action/__init__.py
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/action/_main_task.py
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/action/_main_task_s.c
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/_wy_actions_s.ep.rosidl_typesupport_introspection_c.c
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/__init__.py
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_fastrtps_c.cpython-310-aarch64-linux-gnu.so
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/_wy_actions_s.ep.rosidl_typesupport_c.c
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/libwy_actions__rosidl_generator_py.so
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/_wy_actions_s.ep.rosidl_typesupport_fastrtps_c.c
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_c.cpython-310-aarch64-linux-gnu.so
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_introspection_c.cpython-310-aarch64-linux-gnu.so
Listing '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions'...
Listing '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/action'...
Listing '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/drive'...
Compiling '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/drive/Rosmaster_Lib.py'...
Compiling '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/drive/Twolunzi_driver.py'...
Compiling '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/drive/__init__.py'...
Compiling '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/drive/blue_client.py'...
Compiling '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/drive/blue_server.py'...
Compiling '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/drive/key_check.py'...
Compiling '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/drive/red_client.py'...
Compiling '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/drive/red_server.py'...
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_fastrtps_c.cpython-310-aarch64-linux-gnu.so
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_introspection_c.cpython-310-aarch64-linux-gnu.so
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_c.cpython-310-aarch64-linux-gnu.so
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_generator_py.so
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/action/MainTask.idl
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/action/MainTask.action
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/ament_index/resource_index/package_run_dependencies/wy_actions
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/ament_index/resource_index/parent_prefix_path/wy_actions
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/ament_prefix_path.sh
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/ament_prefix_path.dsv
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/path.sh
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/path.dsv
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/local_setup.bash
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/local_setup.sh
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/local_setup.zsh
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/local_setup.dsv
-- Installing: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.dsv
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/ament_index/resource_index/packages/wy_actions
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_cExport.cmake
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_cExport-noconfig.cmake
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_typesupport_fastrtps_cExport.cmake
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_typesupport_fastrtps_cExport-noconfig.cmake
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_introspection_cExport.cmake
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_introspection_cExport-noconfig.cmake
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_cExport.cmake
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_cExport-noconfig.cmake
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_cppExport.cmake
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_typesupport_fastrtps_cppExport.cmake
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_introspection_cppExport.cmake
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_introspection_cppExport-noconfig.cmake
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_cppExport.cmake
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_cppExport-noconfig.cmake
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_pyExport.cmake
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_pyExport-noconfig.cmake
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/rosidl_cmake-extras.cmake
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/ament_cmake_export_include_directories-extras.cmake
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/ament_cmake_export_libraries-extras.cmake
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actionsConfig.cmake
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actionsConfig-version.cmake
-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.xml
