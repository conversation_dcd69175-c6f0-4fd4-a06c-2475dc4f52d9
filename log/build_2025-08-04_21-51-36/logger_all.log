[2.009s] [34mcolcon[0m [1;30mDEBUG[0m [32mCommand line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'wy_actions'][0m
[2.010s] [34mcolcon[0m [1;30mDEBUG[0m [32mParsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=6, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['wy_actions'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0xffffa74060b0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0xffffa7405ba0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0xffffa7405ba0>>, mixin_verb=('build',))[0m
[2.813s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(colcon_meta) check parameters
[2.814s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(recursive) check parameters
[2.814s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(ignore) check parameters
[2.814s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(path) check parameters
[2.814s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(colcon_meta) discover
[2.814s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(recursive) discover
[2.814s] [34mcolcon.colcon_core.package_discovery[0m [1;30mINFO[0m Crawling recursively for packages in '/home/<USER>/znjy_ws'
[2.814s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extensions ['ignore', 'ignore_ament_install']
[2.815s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extension 'ignore'
[2.815s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extension 'ignore_ament_install'
[2.815s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extensions ['colcon_pkg']
[2.815s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extension 'colcon_pkg'
[2.815s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extensions ['colcon_meta']
[2.815s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extension 'colcon_meta'
[2.815s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extensions ['ros']
[2.815s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extension 'ros'
[2.878s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extensions ['cmake', 'python']
[2.878s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extension 'cmake'
[2.878s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extension 'python'
[2.878s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extensions ['python_setup_py']
[2.879s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extension 'python_setup_py'
[2.879s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(build) by extensions ['ignore', 'ignore_ament_install']
[2.879s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(build) by extension 'ignore'
[2.879s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(build) ignored
[2.880s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(install) by extensions ['ignore', 'ignore_ament_install']
[2.880s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(install) by extension 'ignore'
[2.880s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(install) ignored
[2.881s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(log) by extensions ['ignore', 'ignore_ament_install']
[2.881s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(log) by extension 'ignore'
[2.881s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(log) ignored
[2.882s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extensions ['ignore', 'ignore_ament_install']
[2.882s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extension 'ignore'
[2.882s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extension 'ignore_ament_install'
[2.882s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extensions ['colcon_pkg']
[2.882s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extension 'colcon_pkg'
[2.882s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extensions ['colcon_meta']
[2.883s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extension 'colcon_meta'
[2.883s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extensions ['ros']
[2.883s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extension 'ros'
[2.883s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extensions ['cmake', 'python']
[2.883s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extension 'cmake'
[2.883s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extension 'python'
[2.883s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extensions ['python_setup_py']
[2.883s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extension 'python_setup_py'
[2.884s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/detection) by extensions ['ignore', 'ignore_ament_install']
[2.884s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/detection) by extension 'ignore'
[2.884s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/detection) by extension 'ignore_ament_install'
[2.884s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/detection) by extensions ['colcon_pkg']
[2.884s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/detection) by extension 'colcon_pkg'
[2.885s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/detection) by extensions ['colcon_meta']
[2.885s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/detection) by extension 'colcon_meta'
[2.885s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/detection) by extensions ['ros']
[2.885s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/detection) by extension 'ros'
[2.896s] [34mcolcon.colcon_core.package_identification[0m [1;30mDEBUG[0m [32mPackage 'src/detection' with type 'ros.ament_python' and name 'detection'[0m
[2.897s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/my_servo_control) by extensions ['ignore', 'ignore_ament_install']
[2.897s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/my_servo_control) by extension 'ignore'
[2.897s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/my_servo_control) by extension 'ignore_ament_install'
[2.898s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/my_servo_control) by extensions ['colcon_pkg']
[2.898s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/my_servo_control) by extension 'colcon_pkg'
[2.898s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/my_servo_control) by extensions ['colcon_meta']
[2.898s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/my_servo_control) by extension 'colcon_meta'
[2.898s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/my_servo_control) by extensions ['ros']
[2.898s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/my_servo_control) by extension 'ros'
[2.903s] [34mcolcon.colcon_core.package_identification[0m [1;30mDEBUG[0m [32mPackage 'src/my_servo_control' with type 'ros.ament_python' and name 'my_servo_control'[0m
[2.904s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/usb_cam) by extensions ['ignore', 'ignore_ament_install']
[2.904s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/usb_cam) by extension 'ignore'
[2.904s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/usb_cam) by extension 'ignore_ament_install'
[2.904s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/usb_cam) by extensions ['colcon_pkg']
[2.904s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/usb_cam) by extension 'colcon_pkg'
[2.904s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/usb_cam) by extensions ['colcon_meta']
[2.905s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/usb_cam) by extension 'colcon_meta'
[2.905s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/usb_cam) by extensions ['ros']
[2.905s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/usb_cam) by extension 'ros'
[2.909s] [34mcolcon.colcon_core.package_identification[0m [1;30mDEBUG[0m [32mPackage 'src/usb_cam' with type 'ros.ament_cmake' and name 'usb_cam'[0m
[2.910s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/user_msgs) by extensions ['ignore', 'ignore_ament_install']
[2.910s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/user_msgs) by extension 'ignore'
[2.910s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/user_msgs) by extension 'ignore_ament_install'
[2.911s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/user_msgs) by extensions ['colcon_pkg']
[2.911s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/user_msgs) by extension 'colcon_pkg'
[2.911s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/user_msgs) by extensions ['colcon_meta']
[2.911s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/user_msgs) by extension 'colcon_meta'
[2.911s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/user_msgs) by extensions ['ros']
[2.911s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/user_msgs) by extension 'ros'
[2.914s] [34mcolcon.colcon_core.package_identification[0m [1;30mDEBUG[0m [32mPackage 'src/user_msgs' with type 'ros.ament_cmake' and name 'user_msgs'[0m
[2.914s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/wy_actions) by extensions ['ignore', 'ignore_ament_install']
[2.915s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/wy_actions) by extension 'ignore'
[2.915s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/wy_actions) by extension 'ignore_ament_install'
[2.915s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/wy_actions) by extensions ['colcon_pkg']
[2.915s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/wy_actions) by extension 'colcon_pkg'
[2.915s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/wy_actions) by extensions ['colcon_meta']
[2.915s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/wy_actions) by extension 'colcon_meta'
[2.915s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/wy_actions) by extensions ['ros']
[2.915s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/wy_actions) by extension 'ros'
[2.919s] [34mcolcon.colcon_core.package_identification[0m [1;30mDEBUG[0m [32mPackage 'src/wy_actions' with type 'ros.ament_cmake' and name 'wy_actions'[0m
[2.919s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_bringup) by extensions ['ignore', 'ignore_ament_install']
[2.919s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_bringup) by extension 'ignore'
[2.919s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_bringup) by extension 'ignore_ament_install'
[2.920s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_bringup) by extensions ['colcon_pkg']
[2.920s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_bringup) by extension 'colcon_pkg'
[2.920s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_bringup) by extensions ['colcon_meta']
[2.920s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_bringup) by extension 'colcon_meta'
[2.920s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_bringup) by extensions ['ros']
[2.920s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_bringup) by extension 'ros'
[2.923s] [34mcolcon.colcon_core.package_identification[0m [1;30mDEBUG[0m [32mPackage 'src/yahboomcar_bringup' with type 'ros.ament_python' and name 'yahboomcar_bringup'[0m
[2.923s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_ctrl) by extensions ['ignore', 'ignore_ament_install']
[2.924s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_ctrl) by extension 'ignore'
[2.924s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_ctrl) by extension 'ignore_ament_install'
[2.924s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_ctrl) by extensions ['colcon_pkg']
[2.924s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_ctrl) by extension 'colcon_pkg'
[2.924s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_ctrl) by extensions ['colcon_meta']
[2.924s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_ctrl) by extension 'colcon_meta'
[2.924s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_ctrl) by extensions ['ros']
[2.924s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_ctrl) by extension 'ros'
[2.928s] [34mcolcon.colcon_core.package_identification[0m [1;30mDEBUG[0m [32mPackage 'src/yahboomcar_ctrl' with type 'ros.ament_python' and name 'yahboomcar_ctrl'[0m
[2.929s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_description) by extensions ['ignore', 'ignore_ament_install']
[2.929s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_description) by extension 'ignore'
[2.929s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_description) by extension 'ignore_ament_install'
[2.930s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_description) by extensions ['colcon_pkg']
[2.930s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_description) by extension 'colcon_pkg'
[2.930s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_description) by extensions ['colcon_meta']
[2.930s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_description) by extension 'colcon_meta'
[2.930s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_description) by extensions ['ros']
[2.930s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_description) by extension 'ros'
[2.934s] [34mcolcon.colcon_core.package_identification[0m [1;30mDEBUG[0m [32mPackage 'src/yahboomcar_description' with type 'ros.ament_python' and name 'yahboomcar_description'[0m
[2.935s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(recursive) using defaults
[2.935s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(ignore) discover
[2.935s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(ignore) using defaults
[2.935s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(path) discover
[2.935s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(path) using defaults
[2.990s] [34mcolcon.colcon_core.package_selection[0m [1;30mINFO[0m Skipping not selected package 'my_servo_control' in 'src/my_servo_control'
[2.990s] [34mcolcon.colcon_core.package_selection[0m [1;30mINFO[0m Skipping not selected package 'usb_cam' in 'src/usb_cam'
[2.990s] [34mcolcon.colcon_core.package_selection[0m [1;30mINFO[0m Skipping not selected package 'user_msgs' in 'src/user_msgs'
[2.990s] [34mcolcon.colcon_core.package_selection[0m [1;30mINFO[0m Skipping not selected package 'yahboomcar_bringup' in 'src/yahboomcar_bringup'
[2.990s] [34mcolcon.colcon_core.package_selection[0m [1;30mINFO[0m Skipping not selected package 'yahboomcar_ctrl' in 'src/yahboomcar_ctrl'
[2.990s] [34mcolcon.colcon_core.package_selection[0m [1;30mINFO[0m Skipping not selected package 'yahboomcar_description' in 'src/yahboomcar_description'
[2.990s] [34mcolcon.colcon_core.package_selection[0m [1;30mINFO[0m Skipping not selected package 'detection' in 'src/detection'
[2.992s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(prefix_path) check parameters
[2.992s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(prefix_path) discover
[3.006s] [34mcolcon.colcon_installed_package_information.package_discovery[0m [1;30mDEBUG[0m [32mFound 8 installed packages in /home/<USER>/znjy_ws/install[0m
[3.010s] [34mcolcon.colcon_installed_package_information.package_discovery[0m [1;30mDEBUG[0m [32mFound 2 installed packages in /home/<USER>/ws00_helloworld/install[0m
[3.012s] [34mcolcon.colcon_installed_package_information.package_discovery[0m [1;30mDEBUG[0m [32mFound 1 installed packages in /home/<USER>/yahboom_ws/install[0m
[3.015s] [34mcolcon.colcon_installed_package_information.package_discovery[0m [1;30mDEBUG[0m [32mFound 287 installed packages in /opt/ros/humble[0m
[3.018s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(prefix_path) using defaults
[3.246s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'wy_actions' build argument 'cmake_args' from command line to 'None'
[3.246s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'wy_actions' build argument 'cmake_target' from command line to 'None'
[3.246s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'wy_actions' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[3.246s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'wy_actions' build argument 'cmake_clean_cache' from command line to 'False'
[3.246s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'wy_actions' build argument 'cmake_clean_first' from command line to 'False'
[3.246s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'wy_actions' build argument 'cmake_force_configure' from command line to 'False'
[3.246s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'wy_actions' build argument 'ament_cmake_args' from command line to 'None'
[3.246s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'wy_actions' build argument 'catkin_cmake_args' from command line to 'None'
[3.246s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'wy_actions' build argument 'catkin_skip_building_tests' from command line to 'False'
[3.247s] [34mcolcon.colcon_core.verb[0m [1;30mDEBUG[0m [32mBuilding package 'wy_actions' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/znjy_ws/build/wy_actions', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/znjy_ws/install/wy_actions', 'merge_install': False, 'path': '/home/<USER>/znjy_ws/src/wy_actions', 'symlink_install': False, 'test_result_base': None}[0m
[3.247s] [34mcolcon.colcon_core.executor[0m [1;30mINFO[0m Executing jobs using 'parallel' executor
[3.251s] [34mcolcon.colcon_parallel_executor.executor.parallel[0m [1;30mDEBUG[0m [32mrun_until_complete[0m
[3.252s] [34mcolcon.colcon_ros.task.ament_cmake.build[0m [1;30mINFO[0m Building ROS package in '/home/<USER>/znjy_ws/src/wy_actions' with build type 'ament_cmake'
[3.252s] [34mcolcon.colcon_cmake.task.cmake.build[0m [1;30mINFO[0m Building CMake package in '/home/<USER>/znjy_ws/src/wy_actions'
[3.263s] [34mcolcon.colcon_core.plugin_system[0m [1;30mINFO[0m Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[3.264s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[3.264s] [34mcolcon.colcon_core.shell[0m [1;30mDEBUG[0m [32mSkip shell extension 'dsv' for command environment[0m
[3.300s] [34mcolcon.colcon_core.event_handler.log_command[0m [1;30mDEBUG[0m [32mInvoking command in '/home/<USER>/znjy_ws/build/wy_actions': CMAKE_PREFIX_PATH=/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/znjy_ws/install/user_msgs/lib:/home/<USER>/znjy_ws/install/wy_actions/lib:/home/<USER>/znjy_ws/install/usb_cam/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib:/usr/local/lib:/usr/local/cuda-12.6/lib64 /usr/bin/cmake --build /home/<USER>/znjy_ws/build/wy_actions -- -j6 -l6[0m
[4.670s] [34mcolcon.colcon_core.event_handler.log_command[0m [1;30mDEBUG[0m [32mInvoked command in '/home/<USER>/znjy_ws/build/wy_actions' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/znjy_ws/install/user_msgs/lib:/home/<USER>/znjy_ws/install/wy_actions/lib:/home/<USER>/znjy_ws/install/usb_cam/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib:/usr/local/lib:/usr/local/cuda-12.6/lib64 /usr/bin/cmake --build /home/<USER>/znjy_ws/build/wy_actions -- -j6 -l6[0m
[4.694s] [34mcolcon.colcon_core.event_handler.log_command[0m [1;30mDEBUG[0m [32mInvoking command in '/home/<USER>/znjy_ws/build/wy_actions': CMAKE_PREFIX_PATH=/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/znjy_ws/install/user_msgs/lib:/home/<USER>/znjy_ws/install/wy_actions/lib:/home/<USER>/znjy_ws/install/usb_cam/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib:/usr/local/lib:/usr/local/cuda-12.6/lib64 /usr/bin/cmake --install /home/<USER>/znjy_ws/build/wy_actions[0m
[4.914s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m create_environment_scripts_only(wy_actions)
[4.916s] [34mcolcon.colcon_core.event_handler.log_command[0m [1;30mDEBUG[0m [32mInvoked command in '/home/<USER>/znjy_ws/build/wy_actions' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/znjy_ws/install/user_msgs/lib:/home/<USER>/znjy_ws/install/wy_actions/lib:/home/<USER>/znjy_ws/install/usb_cam/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib:/usr/local/lib:/usr/local/cuda-12.6/lib64 /usr/bin/cmake --install /home/<USER>/znjy_ws/build/wy_actions[0m
[4.929s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m checking '/home/<USER>/znjy_ws/install/wy_actions' for CMake module files
[4.931s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m checking '/home/<USER>/znjy_ws/install/wy_actions' for CMake config files
[4.931s] [34mcolcon.colcon_core.shell[0m [1;30mLevel 1[0m create_environment_hook('wy_actions', 'cmake_prefix_path')
[4.932s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating environment hook '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/hook/cmake_prefix_path.ps1'
[4.937s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating environment descriptor '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/hook/cmake_prefix_path.dsv'
[4.939s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating environment hook '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/hook/cmake_prefix_path.sh'
[4.942s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m checking '/home/<USER>/znjy_ws/install/wy_actions/lib'
[4.942s] [34mcolcon.colcon_core.shell[0m [1;30mLevel 1[0m create_environment_hook('wy_actions', 'ld_library_path_lib')
[4.943s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating environment hook '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/hook/ld_library_path_lib.ps1'
[4.944s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating environment descriptor '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/hook/ld_library_path_lib.dsv'
[4.945s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating environment hook '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/hook/ld_library_path_lib.sh'
[4.945s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m checking '/home/<USER>/znjy_ws/install/wy_actions/bin'
[4.946s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m checking '/home/<USER>/znjy_ws/install/wy_actions/lib/pkgconfig/wy_actions.pc'
[4.946s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m checking '/home/<USER>/znjy_ws/install/wy_actions/lib/python3.10/site-packages'
[4.947s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m checking '/home/<USER>/znjy_ws/install/wy_actions/bin'
[4.947s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating package script '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.ps1'
[4.950s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating package descriptor '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.dsv'
[4.951s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating package script '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.sh'
[4.954s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating package script '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.bash'
[4.960s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating package script '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.zsh'
[4.964s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m create_file_with_runtime_dependencies(/home/<USER>/znjy_ws/install/wy_actions/share/colcon-core/packages/wy_actions)
[4.968s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m create_environment_scripts_only(wy_actions)
[4.972s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m checking '/home/<USER>/znjy_ws/install/wy_actions' for CMake module files
[4.973s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m checking '/home/<USER>/znjy_ws/install/wy_actions' for CMake config files
[4.973s] [34mcolcon.colcon_core.shell[0m [1;30mLevel 1[0m create_environment_hook('wy_actions', 'cmake_prefix_path')
[4.974s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating environment hook '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/hook/cmake_prefix_path.ps1'
[4.976s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating environment descriptor '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/hook/cmake_prefix_path.dsv'
[4.981s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating environment hook '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/hook/cmake_prefix_path.sh'
[4.987s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m checking '/home/<USER>/znjy_ws/install/wy_actions/lib'
[4.988s] [34mcolcon.colcon_core.shell[0m [1;30mLevel 1[0m create_environment_hook('wy_actions', 'ld_library_path_lib')
[4.990s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating environment hook '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/hook/ld_library_path_lib.ps1'
[4.996s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating environment descriptor '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/hook/ld_library_path_lib.dsv'
[4.997s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating environment hook '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/hook/ld_library_path_lib.sh'
[4.999s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m checking '/home/<USER>/znjy_ws/install/wy_actions/bin'
[4.999s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m checking '/home/<USER>/znjy_ws/install/wy_actions/lib/pkgconfig/wy_actions.pc'
[5.000s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m checking '/home/<USER>/znjy_ws/install/wy_actions/lib/python3.10/site-packages'
[5.001s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m checking '/home/<USER>/znjy_ws/install/wy_actions/bin'
[5.002s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating package script '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.ps1'
[5.004s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating package descriptor '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.dsv'
[5.005s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating package script '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.sh'
[5.009s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating package script '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.bash'
[5.011s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating package script '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.zsh'
[5.013s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m create_file_with_runtime_dependencies(/home/<USER>/znjy_ws/install/wy_actions/share/colcon-core/packages/wy_actions)
[5.015s] [34mcolcon.colcon_parallel_executor.executor.parallel[0m [1;30mDEBUG[0m [32mclosing loop[0m
[5.016s] [34mcolcon.colcon_parallel_executor.executor.parallel[0m [1;30mDEBUG[0m [32mloop closed[0m
[5.016s] [34mcolcon.colcon_parallel_executor.executor.parallel[0m [1;30mDEBUG[0m [32mrun_until_complete finished with '0'[0m
[5.017s] [34mcolcon.colcon_core.event_reactor[0m [1;30mDEBUG[0m [32mjoining thread[0m
[5.036s] [34mcolcon.colcon_core.plugin_system[0m [1;30mINFO[0m Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[5.036s] [34mcolcon.colcon_core.plugin_system[0m [1;30mINFO[0m Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[5.036s] [34mcolcon.colcon_notification.desktop_notification[0m [1;30mINFO[0m Sending desktop notification using 'notify2'
[5.093s] [34mcolcon.colcon_core.event_reactor[0m [1;30mDEBUG[0m [32mjoined thread[0m
[5.094s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating prefix script '/home/<USER>/znjy_ws/install/local_setup.ps1'
[5.097s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating prefix util module '/home/<USER>/znjy_ws/install/_local_setup_util_ps1.py'
[5.103s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating prefix chain script '/home/<USER>/znjy_ws/install/setup.ps1'
[5.108s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating prefix script '/home/<USER>/znjy_ws/install/local_setup.sh'
[5.112s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating prefix util module '/home/<USER>/znjy_ws/install/_local_setup_util_sh.py'
[5.115s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating prefix chain script '/home/<USER>/znjy_ws/install/setup.sh'
[5.121s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating prefix script '/home/<USER>/znjy_ws/install/local_setup.bash'
[5.125s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating prefix chain script '/home/<USER>/znjy_ws/install/setup.bash'
[5.135s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating prefix script '/home/<USER>/znjy_ws/install/local_setup.zsh'
[5.136s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating prefix chain script '/home/<USER>/znjy_ws/install/setup.zsh'
