[0.000000] (-) TimerEvent: {}
[0.001887] (-) JobUnselected: {'identifier': 'detection'}
[0.002607] (-) JobUnselected: {'identifier': 'my_servo_control'}
[0.002952] (-) JobUnselected: {'identifier': 'usb_cam'}
[0.003077] (-) JobUnselected: {'identifier': 'user_msgs'}
[0.003120] (-) JobUnselected: {'identifier': 'yahboomcar_bringup'}
[0.003165] (-) JobUnselected: {'identifier': 'yahboomcar_ctrl'}
[0.003199] (-) JobUnselected: {'identifier': 'yahboomcar_description'}
[0.003995] (wy_actions) JobQueued: {'identifier': 'wy_actions', 'dependencies': OrderedDict([('user_msgs', '/home/<USER>/znjy_ws/install/user_msgs')])}
[0.004074] (wy_actions) JobStarted: {'identifier': 'wy_actions'}
[0.037885] (wy_actions) JobProgress: {'identifier': 'wy_actions', 'progress': 'cmake'}
[0.042604] (wy_actions) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/znjy_ws/src/wy_actions', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/znjy_ws/install/wy_actions'], 'cwd': '/home/<USER>/znjy_ws/build/wy_actions', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'jetson'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/znjy_ws/install/user_msgs/lib:/home/<USER>/znjy_ws/install/wy_actions/lib:/home/<USER>/znjy_ws/install/usb_cam/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib:/usr/local/lib:/usr/local/cuda-12.6/lib64'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>/znjy_ws'), ('TERM_PROGRAM_VERSION', '1.99.3'), ('DESKTOP_SESSION', 'ubuntu'), ('NO_PROXY', 'localhost,*********/8,::1'), ('JETSON_L4T', '36.4.3'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('JETSON_MODEL', 'NVIDIA Jetson Orin Nano Engineering Reference Developer Kit Super'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '2446'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=40e36d718764c8131ab2b853688b1f07'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('https_proxy', 'http://127.0.0.1:7890/'), ('COLCON_PREFIX_PATH', '/home/<USER>/znjy_ws/install:/home/<USER>/ws00_helloworld/install:/home/<USER>/yahboom_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'ibus'), ('LOGNAME', 'jetson'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('JETSON_MODULE', 'NVIDIA Jetson Orin Nano (4GB ram)'), ('JETSON_SERIAL_NUMBER', '1423724364572'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'jetson'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/cuda-12.6/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/scripts/noConfigScripts'), ('SESSION_MANAGER', 'local/yahboom:@/tmp/.ICE-unix/2446,unix/yahboom:/tmp/.ICE-unix/2446'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/ac083d08_ceca_4372_adfa_69ba8249bdd6'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-49243217e568589b.txt'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('JETSON_SOC', 'tegra234'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-675997bb9f.sock'), ('GNOME_TERMINAL_SERVICE', ':1.101'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '0'), ('AMENT_PREFIX_PATH', '/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('JETSON_CUDA_ARCH_BIN', '8.7'), ('PWD', '/home/<USER>/znjy_ws/build/wy_actions'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=40e36d718764c8131ab2b853688b1f07'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/znjy_ws/install/yahboomcar_description/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/yahboomcar_ctrl/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/yahboomcar_bringup/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages:/home/<USER>/znjy_ws/install/detection/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/user_msgs/local/lib/python3.10/dist-packages:/home/<USER>/znjy_ws/install/my_servo_control/lib/python3.10/site-packages:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py/lib/python3.10/site-packages:/home/<USER>/yahboom_ws/install/camera/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages:/usr/local/lib/python3.10/site-packages/:'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('JETSON_JETPACK', '6.2'), ('CMAKE_PREFIX_PATH', '/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble'), ('JETSON_P_NUMBER', 'p3767-0004')]), 'shell': False}
[0.099219] (-) TimerEvent: {}
[0.200028] (-) TimerEvent: {}
[0.300926] (-) TimerEvent: {}
[0.401691] (-) TimerEvent: {}
[0.502510] (-) TimerEvent: {}
[0.603218] (-) TimerEvent: {}
[0.703971] (-) TimerEvent: {}
[0.781604] (wy_actions) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[0.804148] (-) TimerEvent: {}
[0.905029] (-) TimerEvent: {}
[1.005839] (-) TimerEvent: {}
[1.079762] (wy_actions) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[1.099536] (wy_actions) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[1.105979] (-) TimerEvent: {}
[1.207507] (-) TimerEvent: {}
[1.306566] (wy_actions) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[1.307593] (-) TimerEvent: {}
[1.323830] (wy_actions) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[1.327510] (wy_actions) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[1.329363] (wy_actions) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[1.337886] (wy_actions) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[1.407809] (-) TimerEvent: {}
[1.505366] (wy_actions) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[1.507919] (-) TimerEvent: {}
[1.520607] (wy_actions) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[1.521628] (wy_actions) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[1.529176] (wy_actions) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[1.542623] (wy_actions) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[1.608127] (-) TimerEvent: {}
[1.708782] (-) TimerEvent: {}
[1.809451] (-) TimerEvent: {}
[1.910290] (-) TimerEvent: {}
[1.965024] (wy_actions) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter \n'}
[2.010490] (-) TimerEvent: {}
[2.111238] (-) TimerEvent: {}
[2.212306] (-) TimerEvent: {}
[2.304927] (wy_actions) StdoutLine: {'line': b'-- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)\n'}
[2.312456] (-) TimerEvent: {}
[2.319927] (wy_actions) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[2.350514] (wy_actions) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[2.412663] (-) TimerEvent: {}
[2.513419] (-) TimerEvent: {}
[2.517763] (wy_actions) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[2.613621] (-) TimerEvent: {}
[2.687110] (wy_actions) StdoutLine: {'line': b'-- Found action_msgs: 1.2.1 (/opt/ros/humble/share/action_msgs/cmake)\n'}
[2.713840] (-) TimerEvent: {}
[2.807536] (wy_actions) StdoutLine: {'line': b'-- Found std_msgs: 4.8.0 (/opt/ros/humble/share/std_msgs/cmake)\n'}
[2.813935] (-) TimerEvent: {}
[2.848072] (wy_actions) StdoutLine: {'line': b'-- Found geometry_msgs: 4.8.0 (/opt/ros/humble/share/geometry_msgs/cmake)\n'}
[2.914114] (-) TimerEvent: {}
[3.015205] (-) TimerEvent: {}
[3.115907] (-) TimerEvent: {}
[3.216691] (-) TimerEvent: {}
[3.317595] (-) TimerEvent: {}
[3.418969] (-) TimerEvent: {}
[3.519858] (-) TimerEvent: {}
[3.620967] (-) TimerEvent: {}
[3.662219] (wy_actions) StdoutLine: {'line': b'-- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)\n'}
[3.721221] (-) TimerEvent: {}
[3.822016] (-) TimerEvent: {}
[3.922793] (-) TimerEvent: {}
[4.023588] (-) TimerEvent: {}
[4.124335] (-) TimerEvent: {}
[4.225318] (-) TimerEvent: {}
[4.303595] (wy_actions) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[4.325597] (-) TimerEvent: {}
[4.426906] (-) TimerEvent: {}
[4.527587] (-) TimerEvent: {}
[4.629488] (-) TimerEvent: {}
[4.730146] (-) TimerEvent: {}
[4.830800] (-) TimerEvent: {}
[4.931494] (-) TimerEvent: {}
[5.032265] (-) TimerEvent: {}
[5.133206] (-) TimerEvent: {}
[5.158520] (wy_actions) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[5.233346] (-) TimerEvent: {}
[5.238257] (wy_actions) StdoutLine: {'line': b'-- Found PythonInterp: /usr/bin/python3 (found suitable version "3.10.12", minimum required is "3.6") \n'}
[5.254533] (wy_actions) StdoutLine: {'line': b'-- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)\n'}
[5.333550] (-) TimerEvent: {}
[5.341755] (wy_actions) StdoutLine: {'line': b'-- Found PythonLibs: /usr/lib/aarch64-linux-gnu/libpython3.10.so (found suitable version "3.10.12", minimum required is "3.5") \n'}
[5.342350] (wy_actions) StdoutLine: {'line': b'-- Using PYTHON_EXECUTABLE: /usr/bin/python3\n'}
[5.342572] (wy_actions) StdoutLine: {'line': b'-- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10\n'}
[5.342760] (wy_actions) StdoutLine: {'line': b'-- Using PYTHON_LIBRARIES: /usr/lib/aarch64-linux-gnu/libpython3.10.so\n'}
[5.402547] (wy_actions) StdoutLine: {'line': b'-- Found PythonExtra: .so  \n'}
[5.433754] (-) TimerEvent: {}
[5.534543] (-) TimerEvent: {}
[5.635238] (-) TimerEvent: {}
[5.735875] (-) TimerEvent: {}
[5.836514] (-) TimerEvent: {}
[5.897289] (wy_actions) StdoutLine: {'line': b'-- Using numpy include directory: /home/<USER>/.local/lib/python3.10/site-packages/numpy/core/include\n'}
[5.936719] (-) TimerEvent: {}
[5.979384] (wy_actions) StdoutLine: {'line': b'-- Configuring done\n'}
[6.036930] (-) TimerEvent: {}
[6.113001] (wy_actions) StdoutLine: {'line': b'-- Generating done\n'}
[6.137131] (-) TimerEvent: {}
[6.144134] (wy_actions) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/znjy_ws/build/wy_actions\n'}
[6.163206] (wy_actions) CommandEnded: {'returncode': 0}
[6.165437] (wy_actions) JobProgress: {'identifier': 'wy_actions', 'progress': 'build'}
[6.174127] (wy_actions) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/znjy_ws/build/wy_actions', '--', '-j6', '-l6'], 'cwd': '/home/<USER>/znjy_ws/build/wy_actions', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'jetson'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/znjy_ws/install/user_msgs/lib:/home/<USER>/znjy_ws/install/wy_actions/lib:/home/<USER>/znjy_ws/install/usb_cam/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib:/usr/local/lib:/usr/local/cuda-12.6/lib64'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>/znjy_ws'), ('TERM_PROGRAM_VERSION', '1.99.3'), ('DESKTOP_SESSION', 'ubuntu'), ('NO_PROXY', 'localhost,*********/8,::1'), ('JETSON_L4T', '36.4.3'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('JETSON_MODEL', 'NVIDIA Jetson Orin Nano Engineering Reference Developer Kit Super'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '2446'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=40e36d718764c8131ab2b853688b1f07'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('https_proxy', 'http://127.0.0.1:7890/'), ('COLCON_PREFIX_PATH', '/home/<USER>/znjy_ws/install:/home/<USER>/ws00_helloworld/install:/home/<USER>/yahboom_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'ibus'), ('LOGNAME', 'jetson'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('JETSON_MODULE', 'NVIDIA Jetson Orin Nano (4GB ram)'), ('JETSON_SERIAL_NUMBER', '1423724364572'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'jetson'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/cuda-12.6/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/scripts/noConfigScripts'), ('SESSION_MANAGER', 'local/yahboom:@/tmp/.ICE-unix/2446,unix/yahboom:/tmp/.ICE-unix/2446'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/ac083d08_ceca_4372_adfa_69ba8249bdd6'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-49243217e568589b.txt'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('JETSON_SOC', 'tegra234'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-675997bb9f.sock'), ('GNOME_TERMINAL_SERVICE', ':1.101'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '0'), ('AMENT_PREFIX_PATH', '/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('JETSON_CUDA_ARCH_BIN', '8.7'), ('PWD', '/home/<USER>/znjy_ws/build/wy_actions'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=40e36d718764c8131ab2b853688b1f07'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/znjy_ws/install/yahboomcar_description/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/yahboomcar_ctrl/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/yahboomcar_bringup/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages:/home/<USER>/znjy_ws/install/detection/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/user_msgs/local/lib/python3.10/dist-packages:/home/<USER>/znjy_ws/install/my_servo_control/lib/python3.10/site-packages:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py/lib/python3.10/site-packages:/home/<USER>/yahboom_ws/install/camera/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages:/usr/local/lib/python3.10/site-packages/:'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('JETSON_JETPACK', '6.2'), ('CMAKE_PREFIX_PATH', '/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble'), ('JETSON_P_NUMBER', 'p3767-0004')]), 'shell': False}
[6.237361] (-) TimerEvent: {}
[6.297140] (wy_actions) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target wy_actions__rosidl_generator_c\x1b[0m\n'}
[6.315592] (wy_actions) StdoutLine: {'line': b'[  3%] Built target wy_actions__cpp\n'}
[6.337528] (-) TimerEvent: {}
[6.345576] (wy_actions) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target wy_actions__rosidl_typesupport_cpp\x1b[0m\n'}
[6.351621] (wy_actions) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target wy_actions__rosidl_typesupport_fastrtps_cpp\x1b[0m\n'}
[6.353471] (wy_actions) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target wy_actions__rosidl_typesupport_introspection_cpp\x1b[0m\n'}
[6.378801] (wy_actions) StdoutLine: {'line': b'[ 12%] Built target wy_actions__rosidl_generator_c\n'}
[6.395473] (wy_actions) StdoutLine: {'line': b'[ 12%] Built target ament_cmake_python_copy_wy_actions\n'}
[6.411582] (wy_actions) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target wy_actions__rosidl_typesupport_introspection_c\x1b[0m\n'}
[6.416266] (wy_actions) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target wy_actions__rosidl_typesupport_fastrtps_c\x1b[0m\n'}
[6.434411] (wy_actions) StdoutLine: {'line': b'[ 22%] Built target wy_actions__rosidl_typesupport_fastrtps_cpp\n'}
[6.437700] (-) TimerEvent: {}
[6.451656] (wy_actions) StdoutLine: {'line': b'[ 32%] Built target wy_actions__rosidl_typesupport_cpp\n'}
[6.463316] (wy_actions) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target wy_actions__rosidl_typesupport_c\x1b[0m\n'}
[6.465476] (wy_actions) StdoutLine: {'line': b'[ 41%] Built target wy_actions__rosidl_typesupport_introspection_c\n'}
[6.474929] (wy_actions) StdoutLine: {'line': b'[ 51%] Built target wy_actions__rosidl_typesupport_fastrtps_c\n'}
[6.480831] (wy_actions) StdoutLine: {'line': b'[ 61%] Built target wy_actions__rosidl_typesupport_introspection_cpp\n'}
[6.492274] (wy_actions) StdoutLine: {'line': b'[ 70%] Built target wy_actions__rosidl_typesupport_c\n'}
[6.537887] (-) TimerEvent: {}
[6.569707] (wy_actions) StdoutLine: {'line': b'[ 70%] Built target wy_actions\n'}
[6.638044] (-) TimerEvent: {}
[6.647274] (wy_actions) StdoutLine: {'line': b'[ 74%] Built target wy_actions__py\n'}
[6.670698] (wy_actions) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target wy_actions__rosidl_generator_py\x1b[0m\n'}
[6.738216] (-) TimerEvent: {}
[6.745356] (wy_actions) StdoutLine: {'line': b'[ 77%] \x1b[32mBuilding C object CMakeFiles/wy_actions__rosidl_generator_py.dir/rosidl_generator_py/wy_actions/action/_main_task_s.c.o\x1b[0m\n'}
[6.838386] (-) TimerEvent: {}
[6.939042] (-) TimerEvent: {}
[7.039853] (-) TimerEvent: {}
[7.140511] (-) TimerEvent: {}
[7.149650] (wy_actions) StdoutLine: {'line': b'[ 80%] \x1b[32m\x1b[1mLinking C shared library rosidl_generator_py/wy_actions/libwy_actions__rosidl_generator_py.so\x1b[0m\n'}
[7.240721] (-) TimerEvent: {}
[7.341446] (-) TimerEvent: {}
[7.364968] (wy_actions) StdoutLine: {'line': b'[ 80%] Built target wy_actions__rosidl_generator_py\n'}
[7.387424] (wy_actions) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target wy_actions__rosidl_typesupport_c__pyext\x1b[0m\n'}
[7.390556] (wy_actions) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target wy_actions__rosidl_typesupport_introspection_c__pyext\x1b[0m\n'}
[7.394791] (wy_actions) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target wy_actions__rosidl_typesupport_fastrtps_c__pyext\x1b[0m\n'}
[7.401191] (wy_actions) StdoutLine: {'line': b'running egg_info\n'}
[7.404377] (wy_actions) StdoutLine: {'line': b'writing wy_actions.egg-info/PKG-INFO\n'}
[7.406670] (wy_actions) StdoutLine: {'line': b'writing dependency_links to wy_actions.egg-info/dependency_links.txt\n'}
[7.407031] (wy_actions) StdoutLine: {'line': b'writing top-level names to wy_actions.egg-info/top_level.txt\n'}
[7.416361] (wy_actions) StdoutLine: {'line': b'[ 87%] \x1b[32mBuilding C object CMakeFiles/wy_actions__rosidl_typesupport_introspection_c__pyext.dir/rosidl_generator_py/wy_actions/_wy_actions_s.ep.rosidl_typesupport_introspection_c.c.o\x1b[0m\n'}
[7.418423] (wy_actions) StdoutLine: {'line': b'[ 87%] \x1b[32mBuilding C object CMakeFiles/wy_actions__rosidl_typesupport_c__pyext.dir/rosidl_generator_py/wy_actions/_wy_actions_s.ep.rosidl_typesupport_c.c.o\x1b[0m\n'}
[7.420281] (wy_actions) StdoutLine: {'line': b"reading manifest file 'wy_actions.egg-info/SOURCES.txt'\n"}
[7.422863] (wy_actions) StdoutLine: {'line': b"writing manifest file 'wy_actions.egg-info/SOURCES.txt'\n"}
[7.432722] (wy_actions) StdoutLine: {'line': b'[ 90%] \x1b[32mBuilding C object CMakeFiles/wy_actions__rosidl_typesupport_fastrtps_c__pyext.dir/rosidl_generator_py/wy_actions/_wy_actions_s.ep.rosidl_typesupport_fastrtps_c.c.o\x1b[0m\n'}
[7.441703] (-) TimerEvent: {}
[7.542944] (-) TimerEvent: {}
[7.547621] (wy_actions) StdoutLine: {'line': b'[ 90%] Built target ament_cmake_python_build_wy_actions_egg\n'}
[7.643138] (-) TimerEvent: {}
[7.744760] (-) TimerEvent: {}
[7.747285] (wy_actions) StdoutLine: {'line': b'[ 93%] \x1b[32m\x1b[1mLinking C shared library rosidl_generator_py/wy_actions/wy_actions_s__rosidl_typesupport_introspection_c.cpython-310-aarch64-linux-gnu.so\x1b[0m\n'}
[7.748442] (wy_actions) StdoutLine: {'line': b'[ 96%] \x1b[32m\x1b[1mLinking C shared library rosidl_generator_py/wy_actions/wy_actions_s__rosidl_typesupport_c.cpython-310-aarch64-linux-gnu.so\x1b[0m\n'}
[7.759514] (wy_actions) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking C shared library rosidl_generator_py/wy_actions/wy_actions_s__rosidl_typesupport_fastrtps_c.cpython-310-aarch64-linux-gnu.so\x1b[0m\n'}
[7.844937] (-) TimerEvent: {}
[7.878676] (wy_actions) StdoutLine: {'line': b'[100%] Built target wy_actions__rosidl_typesupport_c__pyext\n'}
[7.884022] (wy_actions) StdoutLine: {'line': b'[100%] Built target wy_actions__rosidl_typesupport_fastrtps_c__pyext\n'}
[7.884703] (wy_actions) StdoutLine: {'line': b'[100%] Built target wy_actions__rosidl_typesupport_introspection_c__pyext\n'}
[7.919458] (wy_actions) CommandEnded: {'returncode': 0}
[7.921491] (wy_actions) JobProgress: {'identifier': 'wy_actions', 'progress': 'install'}
[7.945698] (-) TimerEvent: {}
[7.946616] (wy_actions) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/znjy_ws/build/wy_actions'], 'cwd': '/home/<USER>/znjy_ws/build/wy_actions', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'jetson'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/znjy_ws/install/user_msgs/lib:/home/<USER>/znjy_ws/install/wy_actions/lib:/home/<USER>/znjy_ws/install/usb_cam/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib:/usr/local/lib:/usr/local/cuda-12.6/lib64'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>/znjy_ws'), ('TERM_PROGRAM_VERSION', '1.99.3'), ('DESKTOP_SESSION', 'ubuntu'), ('NO_PROXY', 'localhost,*********/8,::1'), ('JETSON_L4T', '36.4.3'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('JETSON_MODEL', 'NVIDIA Jetson Orin Nano Engineering Reference Developer Kit Super'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '2446'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=40e36d718764c8131ab2b853688b1f07'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('https_proxy', 'http://127.0.0.1:7890/'), ('COLCON_PREFIX_PATH', '/home/<USER>/znjy_ws/install:/home/<USER>/ws00_helloworld/install:/home/<USER>/yahboom_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'ibus'), ('LOGNAME', 'jetson'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('JETSON_MODULE', 'NVIDIA Jetson Orin Nano (4GB ram)'), ('JETSON_SERIAL_NUMBER', '1423724364572'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'jetson'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/cuda-12.6/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/scripts/noConfigScripts'), ('SESSION_MANAGER', 'local/yahboom:@/tmp/.ICE-unix/2446,unix/yahboom:/tmp/.ICE-unix/2446'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/ac083d08_ceca_4372_adfa_69ba8249bdd6'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-49243217e568589b.txt'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('JETSON_SOC', 'tegra234'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-675997bb9f.sock'), ('GNOME_TERMINAL_SERVICE', ':1.101'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '0'), ('AMENT_PREFIX_PATH', '/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('JETSON_CUDA_ARCH_BIN', '8.7'), ('PWD', '/home/<USER>/znjy_ws/build/wy_actions'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=40e36d718764c8131ab2b853688b1f07'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/znjy_ws/install/yahboomcar_description/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/yahboomcar_ctrl/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/yahboomcar_bringup/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages:/home/<USER>/znjy_ws/install/detection/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/user_msgs/local/lib/python3.10/dist-packages:/home/<USER>/znjy_ws/install/my_servo_control/lib/python3.10/site-packages:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py/lib/python3.10/site-packages:/home/<USER>/yahboom_ws/install/camera/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages:/usr/local/lib/python3.10/site-packages/:'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('JETSON_JETPACK', '6.2'), ('CMAKE_PREFIX_PATH', '/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble'), ('JETSON_P_NUMBER', 'p3767-0004')]), 'shell': False}
[7.998620] (wy_actions) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[8.017977] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/ament_index/resource_index/rosidl_interfaces/wy_actions\n'}
[8.018930] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions\n'}
[8.019371] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action\n'}
[8.020774] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail\n'}
[8.021003] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__struct.h\n'}
[8.021165] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__functions.c\n'}
[8.021309] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__functions.h\n'}
[8.021472] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__type_support.h\n'}
[8.021614] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/main_task.h\n'}
[8.021748] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg\n'}
[8.021879] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg/rosidl_generator_c__visibility_control.h\n'}
[8.022126] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/library_path.sh\n'}
[8.022372] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/library_path.dsv\n'}
[8.023761] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_generator_c.so\n'}
[8.024777] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions\n'}
[8.025836] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action\n'}
[8.026113] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail\n'}
[8.026481] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__rosidl_typesupport_fastrtps_c.h\n'}
[8.026746] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg\n'}
[8.027053] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg/rosidl_typesupport_fastrtps_c__visibility_control.h\n'}
[8.027399] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_fastrtps_c.so\n'}
[8.027674] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions\n'}
[8.027853] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action\n'}
[8.028068] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail\n'}
[8.028245] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__rosidl_typesupport_introspection_c.h\n'}
[8.028461] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__type_support.c\n'}
[8.028636] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg\n'}
[8.028804] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg/rosidl_typesupport_introspection_c__visibility_control.h\n'}
[8.028968] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_introspection_c.so\n'}
[8.029360] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_c.so\n'}
[8.029722] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions\n'}
[8.029940] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action\n'}
[8.030130] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail\n'}
[8.030370] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__traits.hpp\n'}
[8.030540] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__builder.hpp\n'}
[8.030725] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__struct.hpp\n'}
[8.030901] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__type_support.hpp\n'}
[8.031066] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/main_task.hpp\n'}
[8.031180] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg\n'}
[8.031291] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg/rosidl_generator_cpp__visibility_control.hpp\n'}
[8.031436] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions\n'}
[8.031601] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action\n'}
[8.031805] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail\n'}
[8.032037] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[8.032263] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/dds_fastrtps\n'}
[8.032529] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg\n'}
[8.032736] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h\n'}
[8.032910] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_fastrtps_cpp.so\n'}
[8.033079] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions\n'}
[8.033246] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action\n'}
[8.033448] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail\n'}
[8.033591] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__type_support.cpp\n'}
[8.033705] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__rosidl_typesupport_introspection_cpp.hpp\n'}
[8.033813] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_introspection_cpp.so\n'}
[8.034266] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_cpp.so\n'}
[8.034544] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/pythonpath.sh\n'}
[8.034700] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/pythonpath.dsv\n'}
[8.034954] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions-0.0.0-py3.10.egg-info\n'}
[8.035101] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions-0.0.0-py3.10.egg-info/dependency_links.txt\n'}
[8.035337] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions-0.0.0-py3.10.egg-info/top_level.txt\n'}
[8.035564] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions-0.0.0-py3.10.egg-info/SOURCES.txt\n'}
[8.035857] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions-0.0.0-py3.10.egg-info/PKG-INFO\n'}
[8.036627] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions\n'}
[8.037572] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/action\n'}
[8.037896] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/action/__init__.py\n'}
[8.040472] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/action/_main_task.py\n'}
[8.040740] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/action/_main_task_s.c\n'}
[8.040980] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/_wy_actions_s.ep.rosidl_typesupport_introspection_c.c\n'}
[8.041176] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/__init__.py\n'}
[8.041361] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_fastrtps_c.cpython-310-aarch64-linux-gnu.so\n'}
[8.041542] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/_wy_actions_s.ep.rosidl_typesupport_c.c\n'}
[8.041717] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/libwy_actions__rosidl_generator_py.so\n'}
[8.041891] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/_wy_actions_s.ep.rosidl_typesupport_fastrtps_c.c\n'}
[8.042059] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_c.cpython-310-aarch64-linux-gnu.so\n'}
[8.042190] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_introspection_c.cpython-310-aarch64-linux-gnu.so\n'}
[8.045834] (-) TimerEvent: {}
[8.128482] (wy_actions) StdoutLine: {'line': b"Listing '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions'...\n"}
[8.129011] (wy_actions) StdoutLine: {'line': b"Compiling '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/__init__.py'...\n"}
[8.129198] (wy_actions) StdoutLine: {'line': b"Listing '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/action'...\n"}
[8.129343] (wy_actions) StdoutLine: {'line': b"Listing '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/drive'...\n"}
[8.143803] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_fastrtps_c.cpython-310-aarch64-linux-gnu.so\n'}
[8.144496] (wy_actions) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_fastrtps_c.cpython-310-aarch64-linux-gnu.so" to ""\n'}
[8.144771] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_introspection_c.cpython-310-aarch64-linux-gnu.so\n'}
[8.145404] (wy_actions) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_introspection_c.cpython-310-aarch64-linux-gnu.so" to ""\n'}
[8.145787] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_c.cpython-310-aarch64-linux-gnu.so\n'}
[8.146001] (-) TimerEvent: {}
[8.146754] (wy_actions) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_c.cpython-310-aarch64-linux-gnu.so" to ""\n'}
[8.147915] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_generator_py.so\n'}
[8.149250] (wy_actions) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_generator_py.so" to ""\n'}
[8.149563] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/action/MainTask.idl\n'}
[8.149863] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/action/MainTask.action\n'}
[8.150112] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/ament_index/resource_index/package_run_dependencies/wy_actions\n'}
[8.150257] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/share/ament_index/resource_index/parent_prefix_path/wy_actions\n'}
[8.150664] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/ament_prefix_path.sh\n'}
[8.150853] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/ament_prefix_path.dsv\n'}
[8.151043] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/path.sh\n'}
[8.151192] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/path.dsv\n'}
[8.151913] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/local_setup.bash\n'}
[8.152200] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/local_setup.sh\n'}
[8.153032] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/local_setup.zsh\n'}
[8.153579] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/local_setup.dsv\n'}
[8.153832] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.dsv\n'}
[8.153968] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/ament_index/resource_index/packages/wy_actions\n'}
[8.154289] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_cExport.cmake\n'}
[8.154463] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_cExport-noconfig.cmake\n'}
[8.155512] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_typesupport_fastrtps_cExport.cmake\n'}
[8.155778] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_typesupport_fastrtps_cExport-noconfig.cmake\n'}
[8.158492] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_introspection_cExport.cmake\n'}
[8.158913] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_introspection_cExport-noconfig.cmake\n'}
[8.159448] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_cExport.cmake\n'}
[8.159753] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_cExport-noconfig.cmake\n'}
[8.160173] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_cppExport.cmake\n'}
[8.160692] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_typesupport_fastrtps_cppExport.cmake\n'}
[8.160925] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake\n'}
[8.161366] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_introspection_cppExport.cmake\n'}
[8.161567] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_introspection_cppExport-noconfig.cmake\n'}
[8.162015] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_cppExport.cmake\n'}
[8.162223] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_cppExport-noconfig.cmake\n'}
[8.162833] (wy_actions) StdoutLine: {'line': b'-- Old export file "/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_pyExport.cmake" will be replaced.  Removing files [/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_pyExport-noconfig.cmake].\n'}
[8.163128] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_pyExport.cmake\n'}
[8.163571] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_pyExport-noconfig.cmake\n'}
[8.164251] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/rosidl_cmake-extras.cmake\n'}
[8.164474] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[8.164694] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[8.164914] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[8.165124] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/ament_cmake_export_targets-extras.cmake\n'}
[8.165283] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake\n'}
[8.165410] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake\n'}
[8.165532] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actionsConfig.cmake\n'}
[8.165647] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actionsConfig-version.cmake\n'}
[8.165761] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.xml\n'}
[8.171417] (wy_actions) CommandEnded: {'returncode': 0}
[8.245465] (wy_actions) JobEnded: {'identifier': 'wy_actions', 'rc': 0}
[8.247070] (-) TimerEvent: {}
[8.247268] (-) EventReactorShutdown: {}
