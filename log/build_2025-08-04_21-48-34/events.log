[0.000000] (-) TimerEvent: {}
[0.001509] (-) JobUnselected: {'identifier': 'detection'}
[0.001846] (-) JobUnselected: {'identifier': 'my_servo_control'}
[0.001971] (-) JobUnselected: {'identifier': 'usb_cam'}
[0.002320] (-) JobUnselected: {'identifier': 'user_msgs'}
[0.002375] (-) JobUnselected: {'identifier': 'yahboomcar_bringup'}
[0.002417] (-) JobUnselected: {'identifier': 'yahboomcar_ctrl'}
[0.002454] (-) JobUnselected: {'identifier': 'yahboomcar_description'}
[0.002501] (wy_actions) JobQueued: {'identifier': 'wy_actions', 'dependencies': OrderedDict([('user_msgs', '/home/<USER>/znjy_ws/install/user_msgs')])}
[0.002910] (wy_actions) JobStarted: {'identifier': 'wy_actions'}
[0.043841] (wy_actions) JobProgress: {'identifier': 'wy_actions', 'progress': 'cmake'}
[0.046581] (wy_actions) JobProgress: {'identifier': 'wy_actions', 'progress': 'build'}
[0.048644] (wy_actions) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/znjy_ws/build/wy_actions', '--', '-j6', '-l6'], 'cwd': '/home/<USER>/znjy_ws/build/wy_actions', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'jetson'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/znjy_ws/install/user_msgs/lib:/home/<USER>/znjy_ws/install/wy_actions/lib:/home/<USER>/znjy_ws/install/usb_cam/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib:/usr/local/lib:/usr/local/cuda-12.6/lib64'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>/znjy_ws'), ('TERM_PROGRAM_VERSION', '1.99.3'), ('DESKTOP_SESSION', 'ubuntu'), ('NO_PROXY', 'localhost,*********/8,::1'), ('JETSON_L4T', '36.4.3'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('JETSON_MODEL', 'NVIDIA Jetson Orin Nano Engineering Reference Developer Kit Super'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '2446'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=40e36d718764c8131ab2b853688b1f07'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('https_proxy', 'http://127.0.0.1:7890/'), ('COLCON_PREFIX_PATH', '/home/<USER>/znjy_ws/install:/home/<USER>/ws00_helloworld/install:/home/<USER>/yahboom_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'ibus'), ('LOGNAME', 'jetson'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('JETSON_MODULE', 'NVIDIA Jetson Orin Nano (4GB ram)'), ('JETSON_SERIAL_NUMBER', '1423724364572'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'jetson'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/cuda-12.6/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/scripts/noConfigScripts'), ('SESSION_MANAGER', 'local/yahboom:@/tmp/.ICE-unix/2446,unix/yahboom:/tmp/.ICE-unix/2446'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/ac083d08_ceca_4372_adfa_69ba8249bdd6'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-49243217e568589b.txt'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('JETSON_SOC', 'tegra234'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-675997bb9f.sock'), ('GNOME_TERMINAL_SERVICE', ':1.101'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '0'), ('AMENT_PREFIX_PATH', '/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('JETSON_CUDA_ARCH_BIN', '8.7'), ('PWD', '/home/<USER>/znjy_ws/build/wy_actions'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=40e36d718764c8131ab2b853688b1f07'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/znjy_ws/install/yahboomcar_description/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/yahboomcar_ctrl/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/yahboomcar_bringup/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages:/home/<USER>/znjy_ws/install/detection/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/user_msgs/local/lib/python3.10/dist-packages:/home/<USER>/znjy_ws/install/my_servo_control/lib/python3.10/site-packages:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py/lib/python3.10/site-packages:/home/<USER>/yahboom_ws/install/camera/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages:/usr/local/lib/python3.10/site-packages/:'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('JETSON_JETPACK', '6.2'), ('CMAKE_PREFIX_PATH', '/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble'), ('JETSON_P_NUMBER', 'p3767-0004')]), 'shell': False}
[0.099396] (-) TimerEvent: {}
[0.200068] (-) TimerEvent: {}
[0.300654] (-) TimerEvent: {}
[0.401244] (-) TimerEvent: {}
[0.501862] (-) TimerEvent: {}
[0.602490] (-) TimerEvent: {}
[0.676385] (wy_actions) StdoutLine: {'line': b'[  3%] Built target wy_actions__cpp\n'}
[0.701871] (wy_actions) StdoutLine: {'line': b'[  3%] Built target ament_cmake_python_copy_wy_actions\n'}
[0.702556] (-) TimerEvent: {}
[0.738551] (wy_actions) StdoutLine: {'line': b'[ 12%] Built target wy_actions__rosidl_generator_c\n'}
[0.770319] (wy_actions) StdoutLine: {'line': b'[ 22%] Built target wy_actions__rosidl_typesupport_introspection_cpp\n'}
[0.780364] (wy_actions) StdoutLine: {'line': b'[ 32%] Built target wy_actions__rosidl_typesupport_fastrtps_cpp\n'}
[0.796813] (wy_actions) StdoutLine: {'line': b'[ 41%] Built target wy_actions__rosidl_typesupport_cpp\n'}
[0.802741] (-) TimerEvent: {}
[0.826010] (wy_actions) StdoutLine: {'line': b'[ 51%] Built target wy_actions__rosidl_typesupport_introspection_c\n'}
[0.853907] (wy_actions) StdoutLine: {'line': b'[ 61%] Built target wy_actions__rosidl_typesupport_fastrtps_c\n'}
[0.871685] (wy_actions) StdoutLine: {'line': b'[ 70%] Built target wy_actions__rosidl_typesupport_c\n'}
[0.902896] (-) TimerEvent: {}
[0.929686] (wy_actions) StdoutLine: {'line': b'[ 70%] Built target wy_actions\n'}
[0.982288] (wy_actions) StdoutLine: {'line': b'[ 74%] Built target wy_actions__py\n'}
[1.003776] (-) TimerEvent: {}
[1.048303] (wy_actions) StdoutLine: {'line': b'[ 80%] Built target wy_actions__rosidl_generator_py\n'}
[1.103914] (-) TimerEvent: {}
[1.115389] (wy_actions) StdoutLine: {'line': b'[ 87%] Built target wy_actions__rosidl_typesupport_introspection_c__pyext\n'}
[1.123965] (wy_actions) StdoutLine: {'line': b'[ 93%] Built target wy_actions__rosidl_typesupport_c__pyext\n'}
[1.131426] (wy_actions) StdoutLine: {'line': b'[100%] Built target wy_actions__rosidl_typesupport_fastrtps_c__pyext\n'}
[1.204051] (-) TimerEvent: {}
[1.304772] (-) TimerEvent: {}
[1.378975] (wy_actions) StdoutLine: {'line': b'running egg_info\n'}
[1.380423] (wy_actions) StdoutLine: {'line': b'writing wy_actions.egg-info/PKG-INFO\n'}
[1.380926] (wy_actions) StdoutLine: {'line': b'writing dependency_links to wy_actions.egg-info/dependency_links.txt\n'}
[1.381344] (wy_actions) StdoutLine: {'line': b'writing top-level names to wy_actions.egg-info/top_level.txt\n'}
[1.388349] (wy_actions) StdoutLine: {'line': b"reading manifest file 'wy_actions.egg-info/SOURCES.txt'\n"}
[1.394362] (wy_actions) StdoutLine: {'line': b"writing manifest file 'wy_actions.egg-info/SOURCES.txt'\n"}
[1.404958] (-) TimerEvent: {}
[1.476306] (wy_actions) StdoutLine: {'line': b'[100%] Built target ament_cmake_python_build_wy_actions_egg\n'}
[1.505202] (-) TimerEvent: {}
[1.506452] (wy_actions) CommandEnded: {'returncode': 0}
[1.509607] (wy_actions) JobProgress: {'identifier': 'wy_actions', 'progress': 'install'}
[1.544920] (wy_actions) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/znjy_ws/build/wy_actions'], 'cwd': '/home/<USER>/znjy_ws/build/wy_actions', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'jetson'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/znjy_ws/install/user_msgs/lib:/home/<USER>/znjy_ws/install/wy_actions/lib:/home/<USER>/znjy_ws/install/usb_cam/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib:/usr/local/lib:/usr/local/cuda-12.6/lib64'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>/znjy_ws'), ('TERM_PROGRAM_VERSION', '1.99.3'), ('DESKTOP_SESSION', 'ubuntu'), ('NO_PROXY', 'localhost,*********/8,::1'), ('JETSON_L4T', '36.4.3'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('JETSON_MODEL', 'NVIDIA Jetson Orin Nano Engineering Reference Developer Kit Super'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '2446'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=40e36d718764c8131ab2b853688b1f07'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('https_proxy', 'http://127.0.0.1:7890/'), ('COLCON_PREFIX_PATH', '/home/<USER>/znjy_ws/install:/home/<USER>/ws00_helloworld/install:/home/<USER>/yahboom_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'ibus'), ('LOGNAME', 'jetson'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('JETSON_MODULE', 'NVIDIA Jetson Orin Nano (4GB ram)'), ('JETSON_SERIAL_NUMBER', '1423724364572'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'jetson'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/cuda-12.6/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/scripts/noConfigScripts'), ('SESSION_MANAGER', 'local/yahboom:@/tmp/.ICE-unix/2446,unix/yahboom:/tmp/.ICE-unix/2446'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/ac083d08_ceca_4372_adfa_69ba8249bdd6'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-49243217e568589b.txt'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('JETSON_SOC', 'tegra234'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-675997bb9f.sock'), ('GNOME_TERMINAL_SERVICE', ':1.101'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '0'), ('AMENT_PREFIX_PATH', '/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('JETSON_CUDA_ARCH_BIN', '8.7'), ('PWD', '/home/<USER>/znjy_ws/build/wy_actions'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=40e36d718764c8131ab2b853688b1f07'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/znjy_ws/install/yahboomcar_description/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/yahboomcar_ctrl/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/yahboomcar_bringup/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages:/home/<USER>/znjy_ws/install/detection/lib/python3.10/site-packages:/home/<USER>/znjy_ws/install/user_msgs/local/lib/python3.10/dist-packages:/home/<USER>/znjy_ws/install/my_servo_control/lib/python3.10/site-packages:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py/lib/python3.10/site-packages:/home/<USER>/yahboom_ws/install/camera/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages:/usr/local/lib/python3.10/site-packages/:'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('JETSON_JETPACK', '6.2'), ('CMAKE_PREFIX_PATH', '/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble'), ('JETSON_P_NUMBER', 'p3767-0004')]), 'shell': False}
[1.566148] (wy_actions) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[1.575704] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/ament_index/resource_index/rosidl_interfaces/wy_actions\n'}
[1.576889] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions\n'}
[1.577515] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action\n'}
[1.578049] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail\n'}
[1.578504] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__struct.h\n'}
[1.578958] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__functions.c\n'}
[1.579295] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__functions.h\n'}
[1.579621] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__type_support.h\n'}
[1.579972] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/main_task.h\n'}
[1.580277] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg\n'}
[1.580719] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg/rosidl_generator_c__visibility_control.h\n'}
[1.581487] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/library_path.sh\n'}
[1.581910] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/library_path.dsv\n'}
[1.583064] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_generator_c.so\n'}
[1.583710] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions\n'}
[1.584092] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action\n'}
[1.584462] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail\n'}
[1.584859] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__rosidl_typesupport_fastrtps_c.h\n'}
[1.585174] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg\n'}
[1.585566] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg/rosidl_typesupport_fastrtps_c__visibility_control.h\n'}
[1.586505] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_fastrtps_c.so\n'}
[1.587110] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions\n'}
[1.587489] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action\n'}
[1.587829] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail\n'}
[1.588203] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__rosidl_typesupport_introspection_c.h\n'}
[1.588541] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__type_support.c\n'}
[1.588846] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg\n'}
[1.589267] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg/rosidl_typesupport_introspection_c__visibility_control.h\n'}
[1.591879] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_introspection_c.so\n'}
[1.593096] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_c.so\n'}
[1.593678] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions\n'}
[1.594058] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action\n'}
[1.594403] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail\n'}
[1.594777] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__traits.hpp\n'}
[1.595150] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__builder.hpp\n'}
[1.595486] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__struct.hpp\n'}
[1.595814] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__type_support.hpp\n'}
[1.596153] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/main_task.hpp\n'}
[1.596460] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg\n'}
[1.596826] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg/rosidl_generator_cpp__visibility_control.hpp\n'}
[1.597188] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions\n'}
[1.597514] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action\n'}
[1.597850] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail\n'}
[1.598228] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[1.598552] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/dds_fastrtps\n'}
[1.598905] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg\n'}
[1.599289] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h\n'}
[1.600300] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_fastrtps_cpp.so\n'}
[1.600941] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions\n'}
[1.601285] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action\n'}
[1.601635] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail\n'}
[1.602009] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__type_support.cpp\n'}
[1.602378] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__rosidl_typesupport_introspection_cpp.hpp\n'}
[1.603381] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_introspection_cpp.so\n'}
[1.604529] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_cpp.so\n'}
[1.605100] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/pythonpath.sh\n'}
[1.605265] (-) TimerEvent: {}
[1.605689] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/pythonpath.dsv\n'}
[1.607926] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions-0.0.0-py3.10.egg-info\n'}
[1.608880] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions-0.0.0-py3.10.egg-info/dependency_links.txt\n'}
[1.612774] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions-0.0.0-py3.10.egg-info/top_level.txt\n'}
[1.613723] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions-0.0.0-py3.10.egg-info/SOURCES.txt\n'}
[1.614392] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions-0.0.0-py3.10.egg-info/PKG-INFO\n'}
[1.615060] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions\n'}
[1.615615] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/action\n'}
[1.616384] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/action/__init__.py\n'}
[1.617177] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/action/_main_task.py\n'}
[1.617350] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/action/_main_task_s.c\n'}
[1.617496] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/_wy_actions_s.ep.rosidl_typesupport_introspection_c.c\n'}
[1.617623] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/__init__.py\n'}
[1.617741] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_fastrtps_c.cpython-310-aarch64-linux-gnu.so\n'}
[1.617861] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/_wy_actions_s.ep.rosidl_typesupport_c.c\n'}
[1.617974] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/libwy_actions__rosidl_generator_py.so\n'}
[1.618086] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/_wy_actions_s.ep.rosidl_typesupport_fastrtps_c.c\n'}
[1.618389] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_c.cpython-310-aarch64-linux-gnu.so\n'}
[1.618738] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_introspection_c.cpython-310-aarch64-linux-gnu.so\n'}
[1.705411] (-) TimerEvent: {}
[1.724888] (wy_actions) StdoutLine: {'line': b"Listing '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions'...\n"}
[1.725300] (wy_actions) StdoutLine: {'line': b"Listing '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/action'...\n"}
[1.725497] (wy_actions) StdoutLine: {'line': b"Listing '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/drive'...\n"}
[1.725672] (wy_actions) StdoutLine: {'line': b"Compiling '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/drive/red_client.py'...\n"}
[1.725840] (wy_actions) StdoutLine: {'line': b"Compiling '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/drive/red_server.py'...\n"}
[1.740697] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_fastrtps_c.cpython-310-aarch64-linux-gnu.so\n'}
[1.742264] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_introspection_c.cpython-310-aarch64-linux-gnu.so\n'}
[1.743717] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_c.cpython-310-aarch64-linux-gnu.so\n'}
[1.745046] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_generator_py.so\n'}
[1.745729] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/action/MainTask.idl\n'}
[1.746128] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/action/MainTask.action\n'}
[1.746414] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/ament_index/resource_index/package_run_dependencies/wy_actions\n'}
[1.747234] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/ament_index/resource_index/parent_prefix_path/wy_actions\n'}
[1.748959] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/ament_prefix_path.sh\n'}
[1.749271] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/ament_prefix_path.dsv\n'}
[1.749458] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/path.sh\n'}
[1.749627] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/path.dsv\n'}
[1.750117] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/local_setup.bash\n'}
[1.750335] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/local_setup.sh\n'}
[1.750517] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/local_setup.zsh\n'}
[1.750689] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/local_setup.dsv\n'}
[1.750873] (wy_actions) StdoutLine: {'line': b'-- Installing: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.dsv\n'}
[1.751177] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/ament_index/resource_index/packages/wy_actions\n'}
[1.751457] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_cExport.cmake\n'}
[1.751676] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_cExport-noconfig.cmake\n'}
[1.752154] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_typesupport_fastrtps_cExport.cmake\n'}
[1.754056] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_typesupport_fastrtps_cExport-noconfig.cmake\n'}
[1.756121] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_introspection_cExport.cmake\n'}
[1.756293] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_introspection_cExport-noconfig.cmake\n'}
[1.756606] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_cExport.cmake\n'}
[1.757156] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_cExport-noconfig.cmake\n'}
[1.757470] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_cppExport.cmake\n'}
[1.759290] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_typesupport_fastrtps_cppExport.cmake\n'}
[1.759699] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake\n'}
[1.760082] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_introspection_cppExport.cmake\n'}
[1.762734] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_introspection_cppExport-noconfig.cmake\n'}
[1.763045] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_cppExport.cmake\n'}
[1.763248] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_cppExport-noconfig.cmake\n'}
[1.763434] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_pyExport.cmake\n'}
[1.763606] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_pyExport-noconfig.cmake\n'}
[1.767380] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/rosidl_cmake-extras.cmake\n'}
[1.768701] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[1.769271] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[1.769629] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[1.769876] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/ament_cmake_export_targets-extras.cmake\n'}
[1.770107] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake\n'}
[1.770332] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake\n'}
[1.770598] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actionsConfig.cmake\n'}
[1.770864] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actionsConfig-version.cmake\n'}
[1.771334] (wy_actions) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.xml\n'}
[1.783675] (wy_actions) CommandEnded: {'returncode': 0}
[1.805540] (-) TimerEvent: {}
[1.854151] (wy_actions) JobEnded: {'identifier': 'wy_actions', 'rc': 0}
[1.855983] (-) EventReactorShutdown: {}
