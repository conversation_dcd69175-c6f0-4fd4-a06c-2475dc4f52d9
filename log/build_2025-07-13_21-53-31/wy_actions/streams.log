[0.045s] Invoking command in '/home/<USER>/znjy_ws/build/wy_actions': CMAKE_PREFIX_PATH=/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/znjy_ws/install/user_msgs/lib:/home/<USER>/znjy_ws/install/wy_actions/lib:/home/<USER>/znjy_ws/install/usb_cam/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib:/usr/local/lib:/usr/local/cuda-12.6/lib64 /usr/bin/cmake --build /home/<USER>/znjy_ws/build/wy_actions -- -j6 -l6
[0.503s] [  3%] Built target wy_actions__cpp
[0.540s] [  6%] Built target ament_cmake_python_copy_wy_actions
[0.540s] [ 12%] Built target wy_actions__rosidl_generator_c
[0.604s] [ 22%] Built target wy_actions__rosidl_typesupport_introspection_c
[0.606s] [ 32%] Built target wy_actions__rosidl_typesupport_introspection_cpp
[0.612s] [ 48%] Built target wy_actions__rosidl_typesupport_cpp
[0.614s] [ 51%] Built target wy_actions__rosidl_typesupport_fastrtps_cpp
[0.626s] [ 61%] Built target wy_actions__rosidl_typesupport_fastrtps_c
[0.660s] [ 70%] Built target wy_actions__rosidl_typesupport_c
[0.706s] [ 70%] Built target wy_actions
[0.796s] [ 74%] Built target wy_actions__py
[0.884s] [ 80%] Built target wy_actions__rosidl_generator_py
[1.061s] [ 87%] Built target wy_actions__rosidl_typesupport_fastrtps_c__pyext
[1.085s] [ 93%] Built target wy_actions__rosidl_typesupport_c__pyext
[1.102s] [100%] Built target wy_actions__rosidl_typesupport_introspection_c__pyext
[1.327s] running egg_info
[1.329s] writing wy_actions.egg-info/PKG-INFO
[1.329s] writing dependency_links to wy_actions.egg-info/dependency_links.txt
[1.330s] writing top-level names to wy_actions.egg-info/top_level.txt
[1.340s] reading manifest file 'wy_actions.egg-info/SOURCES.txt'
[1.341s] writing manifest file 'wy_actions.egg-info/SOURCES.txt'
[1.417s] [100%] Built target ament_cmake_python_build_wy_actions_egg
[1.446s] Invoked command in '/home/<USER>/znjy_ws/build/wy_actions' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/znjy_ws/install/user_msgs/lib:/home/<USER>/znjy_ws/install/wy_actions/lib:/home/<USER>/znjy_ws/install/usb_cam/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib:/usr/local/lib:/usr/local/cuda-12.6/lib64 /usr/bin/cmake --build /home/<USER>/znjy_ws/build/wy_actions -- -j6 -l6
[1.469s] Invoking command in '/home/<USER>/znjy_ws/build/wy_actions': CMAKE_PREFIX_PATH=/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/znjy_ws/install/user_msgs/lib:/home/<USER>/znjy_ws/install/wy_actions/lib:/home/<USER>/znjy_ws/install/usb_cam/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib:/usr/local/lib:/usr/local/cuda-12.6/lib64 /usr/bin/cmake --install /home/<USER>/znjy_ws/build/wy_actions
[1.488s] -- Install configuration: ""
[1.495s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/ament_index/resource_index/rosidl_interfaces/wy_actions
[1.496s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions
[1.496s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action
[1.496s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail
[1.496s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__struct.h
[1.496s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__functions.c
[1.496s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__functions.h
[1.496s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__type_support.h
[1.497s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/main_task.h
[1.497s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg
[1.497s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg/rosidl_generator_c__visibility_control.h
[1.497s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/library_path.sh
[1.497s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/library_path.dsv
[1.498s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_generator_c.so
[1.498s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions
[1.498s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action
[1.498s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail
[1.498s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__rosidl_typesupport_fastrtps_c.h
[1.499s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg
[1.499s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg/rosidl_typesupport_fastrtps_c__visibility_control.h
[1.499s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_fastrtps_c.so
[1.499s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions
[1.500s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action
[1.500s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail
[1.500s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__rosidl_typesupport_introspection_c.h
[1.500s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__type_support.c
[1.500s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg
[1.500s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg/rosidl_typesupport_introspection_c__visibility_control.h
[1.501s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_introspection_c.so
[1.510s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_c.so
[1.510s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions
[1.511s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action
[1.511s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail
[1.511s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__traits.hpp
[1.511s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__builder.hpp
[1.511s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__struct.hpp
[1.511s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__type_support.hpp
[1.511s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/main_task.hpp
[1.512s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg
[1.512s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg/rosidl_generator_cpp__visibility_control.hpp
[1.512s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions
[1.512s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action
[1.512s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail
[1.512s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__rosidl_typesupport_fastrtps_cpp.hpp
[1.512s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/dds_fastrtps
[1.513s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg
[1.513s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h
[1.513s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_fastrtps_cpp.so
[1.513s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions
[1.513s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action
[1.514s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail
[1.514s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__type_support.cpp
[1.514s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__rosidl_typesupport_introspection_cpp.hpp
[1.515s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_introspection_cpp.so
[1.515s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_cpp.so
[1.516s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/pythonpath.sh
[1.516s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/pythonpath.dsv
[1.517s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions-0.0.0-py3.10.egg-info
[1.517s] -- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions-0.0.0-py3.10.egg-info/dependency_links.txt
[1.517s] -- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions-0.0.0-py3.10.egg-info/top_level.txt
[1.517s] -- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions-0.0.0-py3.10.egg-info/SOURCES.txt
[1.517s] -- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions-0.0.0-py3.10.egg-info/PKG-INFO
[1.517s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions
[1.521s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/action
[1.522s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/action/__init__.py
[1.522s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/action/_main_task.py
[1.522s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/action/_main_task_s.c
[1.523s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/_wy_actions_s.ep.rosidl_typesupport_introspection_c.c
[1.523s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/__init__.py
[1.523s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_fastrtps_c.cpython-310-aarch64-linux-gnu.so
[1.524s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/_wy_actions_s.ep.rosidl_typesupport_c.c
[1.524s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/libwy_actions__rosidl_generator_py.so
[1.524s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/_wy_actions_s.ep.rosidl_typesupport_fastrtps_c.c
[1.524s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_c.cpython-310-aarch64-linux-gnu.so
[1.524s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_introspection_c.cpython-310-aarch64-linux-gnu.so
[1.613s] Listing '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions'...
[1.614s] Listing '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/action'...
[1.614s] Listing '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/drive'...
[1.614s] Compiling '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/drive/red_client.py'...
[1.614s] Compiling '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/drive/red_server.py'...
[1.627s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_fastrtps_c.cpython-310-aarch64-linux-gnu.so
[1.628s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_introspection_c.cpython-310-aarch64-linux-gnu.so
[1.628s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_c.cpython-310-aarch64-linux-gnu.so
[1.629s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_generator_py.so
[1.630s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/action/MainTask.idl
[1.630s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/action/MainTask.action
[1.630s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/ament_index/resource_index/package_run_dependencies/wy_actions
[1.631s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/ament_index/resource_index/parent_prefix_path/wy_actions
[1.631s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/ament_prefix_path.sh
[1.631s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/ament_prefix_path.dsv
[1.631s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/path.sh
[1.631s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/path.dsv
[1.631s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/local_setup.bash
[1.632s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/local_setup.sh
[1.632s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/local_setup.zsh
[1.632s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/local_setup.dsv
[1.632s] -- Installing: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.dsv
[1.633s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/ament_index/resource_index/packages/wy_actions
[1.635s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_cExport.cmake
[1.635s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_cExport-noconfig.cmake
[1.635s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_typesupport_fastrtps_cExport.cmake
[1.635s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_typesupport_fastrtps_cExport-noconfig.cmake
[1.638s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_introspection_cExport.cmake
[1.638s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_introspection_cExport-noconfig.cmake
[1.641s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_cExport.cmake
[1.642s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_cExport-noconfig.cmake
[1.642s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_cppExport.cmake
[1.642s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_typesupport_fastrtps_cppExport.cmake
[1.643s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake
[1.643s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_introspection_cppExport.cmake
[1.643s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_introspection_cppExport-noconfig.cmake
[1.643s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_cppExport.cmake
[1.643s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_cppExport-noconfig.cmake
[1.643s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_pyExport.cmake
[1.643s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_pyExport-noconfig.cmake
[1.644s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/rosidl_cmake-extras.cmake
[1.644s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/ament_cmake_export_dependencies-extras.cmake
[1.644s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/ament_cmake_export_include_directories-extras.cmake
[1.644s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/ament_cmake_export_libraries-extras.cmake
[1.644s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/ament_cmake_export_targets-extras.cmake
[1.644s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake
[1.644s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake
[1.644s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actionsConfig.cmake
[1.644s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actionsConfig-version.cmake
[1.645s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.xml
[1.648s] Invoked command in '/home/<USER>/znjy_ws/build/wy_actions' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/znjy_ws/install/user_msgs/lib:/home/<USER>/znjy_ws/install/wy_actions/lib:/home/<USER>/znjy_ws/install/usb_cam/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib:/usr/local/lib:/usr/local/cuda-12.6/lib64 /usr/bin/cmake --install /home/<USER>/znjy_ws/build/wy_actions
