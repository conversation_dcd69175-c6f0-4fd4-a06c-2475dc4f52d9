[0.378s] [34mcolcon[0m [1;30mDEBUG[0m [32mCommand line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'wy_actions'][0m
[0.379s] [34mcolcon[0m [1;30mDEBUG[0m [32mParsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=6, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['wy_actions'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0xffff975560b0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0xffff97555ba0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0xffff97555ba0>>, mixin_verb=('build',))[0m
[0.956s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(colcon_meta) check parameters
[0.957s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(recursive) check parameters
[0.957s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(ignore) check parameters
[0.957s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(path) check parameters
[0.957s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(colcon_meta) discover
[0.957s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(recursive) discover
[0.957s] [34mcolcon.colcon_core.package_discovery[0m [1;30mINFO[0m Crawling recursively for packages in '/home/<USER>/znjy_ws'
[0.957s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.958s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extension 'ignore'
[0.958s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extension 'ignore_ament_install'
[0.958s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extensions ['colcon_pkg']
[0.958s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extension 'colcon_pkg'
[0.958s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extensions ['colcon_meta']
[0.958s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extension 'colcon_meta'
[0.958s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extensions ['ros']
[0.958s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extension 'ros'
[1.018s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extensions ['cmake', 'python']
[1.018s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extension 'cmake'
[1.019s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extension 'python'
[1.019s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extensions ['python_setup_py']
[1.019s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extension 'python_setup_py'
[1.019s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(build) by extensions ['ignore', 'ignore_ament_install']
[1.019s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(build) by extension 'ignore'
[1.019s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(build) ignored
[1.020s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(install) by extensions ['ignore', 'ignore_ament_install']
[1.020s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(install) by extension 'ignore'
[1.020s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(install) ignored
[1.020s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(log) by extensions ['ignore', 'ignore_ament_install']
[1.020s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(log) by extension 'ignore'
[1.020s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(log) ignored
[1.021s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extensions ['ignore', 'ignore_ament_install']
[1.021s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extension 'ignore'
[1.021s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extension 'ignore_ament_install'
[1.021s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extensions ['colcon_pkg']
[1.021s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extension 'colcon_pkg'
[1.021s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extensions ['colcon_meta']
[1.021s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extension 'colcon_meta'
[1.021s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extensions ['ros']
[1.021s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extension 'ros'
[1.022s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extensions ['cmake', 'python']
[1.022s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extension 'cmake'
[1.022s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extension 'python'
[1.022s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extensions ['python_setup_py']
[1.022s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extension 'python_setup_py'
[1.022s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/detection) by extensions ['ignore', 'ignore_ament_install']
[1.022s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/detection) by extension 'ignore'
[1.022s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/detection) by extension 'ignore_ament_install'
[1.022s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/detection) by extensions ['colcon_pkg']
[1.023s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/detection) by extension 'colcon_pkg'
[1.023s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/detection) by extensions ['colcon_meta']
[1.023s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/detection) by extension 'colcon_meta'
[1.023s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/detection) by extensions ['ros']
[1.023s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/detection) by extension 'ros'
[1.029s] [34mcolcon.colcon_core.package_identification[0m [1;30mDEBUG[0m [32mPackage 'src/detection' with type 'ros.ament_python' and name 'detection'[0m
[1.030s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/my_servo_control) by extensions ['ignore', 'ignore_ament_install']
[1.030s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/my_servo_control) by extension 'ignore'
[1.030s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/my_servo_control) by extension 'ignore_ament_install'
[1.030s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/my_servo_control) by extensions ['colcon_pkg']
[1.030s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/my_servo_control) by extension 'colcon_pkg'
[1.031s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/my_servo_control) by extensions ['colcon_meta']
[1.031s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/my_servo_control) by extension 'colcon_meta'
[1.031s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/my_servo_control) by extensions ['ros']
[1.031s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/my_servo_control) by extension 'ros'
[1.032s] [34mcolcon.colcon_core.package_identification[0m [1;30mDEBUG[0m [32mPackage 'src/my_servo_control' with type 'ros.ament_python' and name 'my_servo_control'[0m
[1.033s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/usb_cam) by extensions ['ignore', 'ignore_ament_install']
[1.033s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/usb_cam) by extension 'ignore'
[1.033s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/usb_cam) by extension 'ignore_ament_install'
[1.033s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/usb_cam) by extensions ['colcon_pkg']
[1.033s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/usb_cam) by extension 'colcon_pkg'
[1.033s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/usb_cam) by extensions ['colcon_meta']
[1.033s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/usb_cam) by extension 'colcon_meta'
[1.033s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/usb_cam) by extensions ['ros']
[1.033s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/usb_cam) by extension 'ros'
[1.037s] [34mcolcon.colcon_core.package_identification[0m [1;30mDEBUG[0m [32mPackage 'src/usb_cam' with type 'ros.ament_cmake' and name 'usb_cam'[0m
[1.037s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/user_msgs) by extensions ['ignore', 'ignore_ament_install']
[1.038s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/user_msgs) by extension 'ignore'
[1.038s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/user_msgs) by extension 'ignore_ament_install'
[1.038s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/user_msgs) by extensions ['colcon_pkg']
[1.038s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/user_msgs) by extension 'colcon_pkg'
[1.038s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/user_msgs) by extensions ['colcon_meta']
[1.038s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/user_msgs) by extension 'colcon_meta'
[1.038s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/user_msgs) by extensions ['ros']
[1.038s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/user_msgs) by extension 'ros'
[1.040s] [34mcolcon.colcon_core.package_identification[0m [1;30mDEBUG[0m [32mPackage 'src/user_msgs' with type 'ros.ament_cmake' and name 'user_msgs'[0m
[1.041s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/wy_actions) by extensions ['ignore', 'ignore_ament_install']
[1.041s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/wy_actions) by extension 'ignore'
[1.041s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/wy_actions) by extension 'ignore_ament_install'
[1.041s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/wy_actions) by extensions ['colcon_pkg']
[1.041s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/wy_actions) by extension 'colcon_pkg'
[1.041s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/wy_actions) by extensions ['colcon_meta']
[1.041s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/wy_actions) by extension 'colcon_meta'
[1.041s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/wy_actions) by extensions ['ros']
[1.041s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/wy_actions) by extension 'ros'
[1.043s] [34mcolcon.colcon_core.package_identification[0m [1;30mDEBUG[0m [32mPackage 'src/wy_actions' with type 'ros.ament_cmake' and name 'wy_actions'[0m
[1.044s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_bringup) by extensions ['ignore', 'ignore_ament_install']
[1.044s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_bringup) by extension 'ignore'
[1.044s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_bringup) by extension 'ignore_ament_install'
[1.044s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_bringup) by extensions ['colcon_pkg']
[1.044s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_bringup) by extension 'colcon_pkg'
[1.044s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_bringup) by extensions ['colcon_meta']
[1.044s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_bringup) by extension 'colcon_meta'
[1.045s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_bringup) by extensions ['ros']
[1.045s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_bringup) by extension 'ros'
[1.046s] [34mcolcon.colcon_core.package_identification[0m [1;30mDEBUG[0m [32mPackage 'src/yahboomcar_bringup' with type 'ros.ament_python' and name 'yahboomcar_bringup'[0m
[1.046s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_ctrl) by extensions ['ignore', 'ignore_ament_install']
[1.046s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_ctrl) by extension 'ignore'
[1.047s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_ctrl) by extension 'ignore_ament_install'
[1.047s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_ctrl) by extensions ['colcon_pkg']
[1.047s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_ctrl) by extension 'colcon_pkg'
[1.047s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_ctrl) by extensions ['colcon_meta']
[1.047s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_ctrl) by extension 'colcon_meta'
[1.047s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_ctrl) by extensions ['ros']
[1.047s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_ctrl) by extension 'ros'
[1.048s] [34mcolcon.colcon_core.package_identification[0m [1;30mDEBUG[0m [32mPackage 'src/yahboomcar_ctrl' with type 'ros.ament_python' and name 'yahboomcar_ctrl'[0m
[1.049s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_description) by extensions ['ignore', 'ignore_ament_install']
[1.049s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_description) by extension 'ignore'
[1.049s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_description) by extension 'ignore_ament_install'
[1.049s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_description) by extensions ['colcon_pkg']
[1.049s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_description) by extension 'colcon_pkg'
[1.049s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_description) by extensions ['colcon_meta']
[1.049s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_description) by extension 'colcon_meta'
[1.049s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_description) by extensions ['ros']
[1.049s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src/yahboomcar_description) by extension 'ros'
[1.053s] [34mcolcon.colcon_core.package_identification[0m [1;30mDEBUG[0m [32mPackage 'src/yahboomcar_description' with type 'ros.ament_python' and name 'yahboomcar_description'[0m
[1.053s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(recursive) using defaults
[1.053s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(ignore) discover
[1.053s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(ignore) using defaults
[1.054s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(path) discover
[1.054s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(path) using defaults
[1.089s] [34mcolcon.colcon_core.package_selection[0m [1;30mINFO[0m Skipping not selected package 'my_servo_control' in 'src/my_servo_control'
[1.089s] [34mcolcon.colcon_core.package_selection[0m [1;30mINFO[0m Skipping not selected package 'usb_cam' in 'src/usb_cam'
[1.090s] [34mcolcon.colcon_core.package_selection[0m [1;30mINFO[0m Skipping not selected package 'user_msgs' in 'src/user_msgs'
[1.090s] [34mcolcon.colcon_core.package_selection[0m [1;30mINFO[0m Skipping not selected package 'yahboomcar_bringup' in 'src/yahboomcar_bringup'
[1.090s] [34mcolcon.colcon_core.package_selection[0m [1;30mINFO[0m Skipping not selected package 'yahboomcar_ctrl' in 'src/yahboomcar_ctrl'
[1.090s] [34mcolcon.colcon_core.package_selection[0m [1;30mINFO[0m Skipping not selected package 'yahboomcar_description' in 'src/yahboomcar_description'
[1.090s] [34mcolcon.colcon_core.package_selection[0m [1;30mINFO[0m Skipping not selected package 'detection' in 'src/detection'
[1.091s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(prefix_path) check parameters
[1.091s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(prefix_path) discover
[1.097s] [34mcolcon.colcon_installed_package_information.package_discovery[0m [1;30mDEBUG[0m [32mFound 8 installed packages in /home/<USER>/znjy_ws/install[0m
[1.098s] [34mcolcon.colcon_installed_package_information.package_discovery[0m [1;30mDEBUG[0m [32mFound 2 installed packages in /home/<USER>/ws00_helloworld/install[0m
[1.099s] [34mcolcon.colcon_installed_package_information.package_discovery[0m [1;30mDEBUG[0m [32mFound 1 installed packages in /home/<USER>/yahboom_ws/install[0m
[1.101s] [34mcolcon.colcon_installed_package_information.package_discovery[0m [1;30mDEBUG[0m [32mFound 287 installed packages in /opt/ros/humble[0m
[1.104s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(prefix_path) using defaults
[1.272s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'wy_actions' build argument 'cmake_args' from command line to 'None'
[1.272s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'wy_actions' build argument 'cmake_target' from command line to 'None'
[1.272s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'wy_actions' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[1.272s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'wy_actions' build argument 'cmake_clean_cache' from command line to 'False'
[1.272s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'wy_actions' build argument 'cmake_clean_first' from command line to 'False'
[1.272s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'wy_actions' build argument 'cmake_force_configure' from command line to 'False'
[1.272s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'wy_actions' build argument 'ament_cmake_args' from command line to 'None'
[1.273s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'wy_actions' build argument 'catkin_cmake_args' from command line to 'None'
[1.273s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'wy_actions' build argument 'catkin_skip_building_tests' from command line to 'False'
[1.273s] [34mcolcon.colcon_core.verb[0m [1;30mDEBUG[0m [32mBuilding package 'wy_actions' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/znjy_ws/build/wy_actions', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/znjy_ws/install/wy_actions', 'merge_install': False, 'path': '/home/<USER>/znjy_ws/src/wy_actions', 'symlink_install': False, 'test_result_base': None}[0m
[1.273s] [34mcolcon.colcon_core.executor[0m [1;30mINFO[0m Executing jobs using 'parallel' executor
[1.275s] [34mcolcon.colcon_parallel_executor.executor.parallel[0m [1;30mDEBUG[0m [32mrun_until_complete[0m
[1.276s] [34mcolcon.colcon_ros.task.ament_cmake.build[0m [1;30mINFO[0m Building ROS package in '/home/<USER>/znjy_ws/src/wy_actions' with build type 'ament_cmake'
[1.276s] [34mcolcon.colcon_cmake.task.cmake.build[0m [1;30mINFO[0m Building CMake package in '/home/<USER>/znjy_ws/src/wy_actions'
[1.283s] [34mcolcon.colcon_core.plugin_system[0m [1;30mINFO[0m Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[1.284s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.284s] [34mcolcon.colcon_core.shell[0m [1;30mDEBUG[0m [32mSkip shell extension 'dsv' for command environment[0m
[1.314s] [34mcolcon.colcon_core.event_handler.log_command[0m [1;30mDEBUG[0m [32mInvoking command in '/home/<USER>/znjy_ws/build/wy_actions': CMAKE_PREFIX_PATH=/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/znjy_ws/install/user_msgs/lib:/home/<USER>/znjy_ws/install/wy_actions/lib:/home/<USER>/znjy_ws/install/usb_cam/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib:/usr/local/lib:/usr/local/cuda-12.6/lib64 /usr/bin/cmake --build /home/<USER>/znjy_ws/build/wy_actions -- -j6 -l6[0m
[2.361s] [34mcolcon.colcon_core.event_handler.log_command[0m [1;30mDEBUG[0m [32mInvoked command in '/home/<USER>/znjy_ws/build/wy_actions' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/znjy_ws/install/user_msgs/lib:/home/<USER>/znjy_ws/install/wy_actions/lib:/home/<USER>/znjy_ws/install/usb_cam/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib:/usr/local/lib:/usr/local/cuda-12.6/lib64 /usr/bin/cmake --build /home/<USER>/znjy_ws/build/wy_actions -- -j6 -l6[0m
[2.390s] [34mcolcon.colcon_core.event_handler.log_command[0m [1;30mDEBUG[0m [32mInvoking command in '/home/<USER>/znjy_ws/build/wy_actions': CMAKE_PREFIX_PATH=/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/znjy_ws/install/user_msgs/lib:/home/<USER>/znjy_ws/install/wy_actions/lib:/home/<USER>/znjy_ws/install/usb_cam/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib:/usr/local/lib:/usr/local/cuda-12.6/lib64 /usr/bin/cmake --install /home/<USER>/znjy_ws/build/wy_actions[0m
[2.574s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m create_environment_scripts_only(wy_actions)
[2.576s] [34mcolcon.colcon_core.event_handler.log_command[0m [1;30mDEBUG[0m [32mInvoked command in '/home/<USER>/znjy_ws/build/wy_actions' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/znjy_ws/install/user_msgs/lib:/home/<USER>/znjy_ws/install/wy_actions/lib:/home/<USER>/znjy_ws/install/usb_cam/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib:/usr/local/lib:/usr/local/cuda-12.6/lib64 /usr/bin/cmake --install /home/<USER>/znjy_ws/build/wy_actions[0m
[2.588s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m checking '/home/<USER>/znjy_ws/install/wy_actions' for CMake module files
[2.590s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m checking '/home/<USER>/znjy_ws/install/wy_actions' for CMake config files
[2.590s] [34mcolcon.colcon_core.shell[0m [1;30mLevel 1[0m create_environment_hook('wy_actions', 'cmake_prefix_path')
[2.591s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating environment hook '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/hook/cmake_prefix_path.ps1'
[2.593s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating environment descriptor '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/hook/cmake_prefix_path.dsv'
[2.595s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating environment hook '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/hook/cmake_prefix_path.sh'
[2.599s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m checking '/home/<USER>/znjy_ws/install/wy_actions/lib'
[2.599s] [34mcolcon.colcon_core.shell[0m [1;30mLevel 1[0m create_environment_hook('wy_actions', 'ld_library_path_lib')
[2.600s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating environment hook '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/hook/ld_library_path_lib.ps1'
[2.602s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating environment descriptor '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/hook/ld_library_path_lib.dsv'
[2.603s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating environment hook '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/hook/ld_library_path_lib.sh'
[2.604s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m checking '/home/<USER>/znjy_ws/install/wy_actions/bin'
[2.604s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m checking '/home/<USER>/znjy_ws/install/wy_actions/lib/pkgconfig/wy_actions.pc'
[2.605s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m checking '/home/<USER>/znjy_ws/install/wy_actions/lib/python3.10/site-packages'
[2.605s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m checking '/home/<USER>/znjy_ws/install/wy_actions/bin'
[2.606s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating package script '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.ps1'
[2.609s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating package descriptor '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.dsv'
[2.611s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating package script '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.sh'
[2.613s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating package script '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.bash'
[2.615s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating package script '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.zsh'
[2.617s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m create_file_with_runtime_dependencies(/home/<USER>/znjy_ws/install/wy_actions/share/colcon-core/packages/wy_actions)
[2.620s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m create_environment_scripts_only(wy_actions)
[2.621s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m checking '/home/<USER>/znjy_ws/install/wy_actions' for CMake module files
[2.623s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m checking '/home/<USER>/znjy_ws/install/wy_actions' for CMake config files
[2.623s] [34mcolcon.colcon_core.shell[0m [1;30mLevel 1[0m create_environment_hook('wy_actions', 'cmake_prefix_path')
[2.624s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating environment hook '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/hook/cmake_prefix_path.ps1'
[2.626s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating environment descriptor '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/hook/cmake_prefix_path.dsv'
[2.627s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating environment hook '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/hook/cmake_prefix_path.sh'
[2.628s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m checking '/home/<USER>/znjy_ws/install/wy_actions/lib'
[2.628s] [34mcolcon.colcon_core.shell[0m [1;30mLevel 1[0m create_environment_hook('wy_actions', 'ld_library_path_lib')
[2.629s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating environment hook '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/hook/ld_library_path_lib.ps1'
[2.630s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating environment descriptor '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/hook/ld_library_path_lib.dsv'
[2.631s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating environment hook '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/hook/ld_library_path_lib.sh'
[2.632s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m checking '/home/<USER>/znjy_ws/install/wy_actions/bin'
[2.633s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m checking '/home/<USER>/znjy_ws/install/wy_actions/lib/pkgconfig/wy_actions.pc'
[2.633s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m checking '/home/<USER>/znjy_ws/install/wy_actions/lib/python3.10/site-packages'
[2.634s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m checking '/home/<USER>/znjy_ws/install/wy_actions/bin'
[2.635s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating package script '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.ps1'
[2.636s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating package descriptor '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.dsv'
[2.638s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating package script '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.sh'
[2.639s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating package script '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.bash'
[2.640s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating package script '/home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.zsh'
[2.641s] [34mcolcon.colcon_core.environment[0m [1;30mLevel 1[0m create_file_with_runtime_dependencies(/home/<USER>/znjy_ws/install/wy_actions/share/colcon-core/packages/wy_actions)
[2.642s] [34mcolcon.colcon_parallel_executor.executor.parallel[0m [1;30mDEBUG[0m [32mclosing loop[0m
[2.642s] [34mcolcon.colcon_parallel_executor.executor.parallel[0m [1;30mDEBUG[0m [32mloop closed[0m
[2.643s] [34mcolcon.colcon_parallel_executor.executor.parallel[0m [1;30mDEBUG[0m [32mrun_until_complete finished with '0'[0m
[2.643s] [34mcolcon.colcon_core.event_reactor[0m [1;30mDEBUG[0m [32mjoining thread[0m
[2.662s] [34mcolcon.colcon_core.plugin_system[0m [1;30mINFO[0m Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[2.663s] [34mcolcon.colcon_core.plugin_system[0m [1;30mINFO[0m Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[2.663s] [34mcolcon.colcon_notification.desktop_notification[0m [1;30mINFO[0m Sending desktop notification using 'notify2'
[2.754s] [34mcolcon.colcon_core.event_reactor[0m [1;30mDEBUG[0m [32mjoined thread[0m
[2.755s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating prefix script '/home/<USER>/znjy_ws/install/local_setup.ps1'
[2.761s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating prefix util module '/home/<USER>/znjy_ws/install/_local_setup_util_ps1.py'
[2.765s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating prefix chain script '/home/<USER>/znjy_ws/install/setup.ps1'
[2.771s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating prefix script '/home/<USER>/znjy_ws/install/local_setup.sh'
[2.774s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating prefix util module '/home/<USER>/znjy_ws/install/_local_setup_util_sh.py'
[2.775s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating prefix chain script '/home/<USER>/znjy_ws/install/setup.sh'
[2.780s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating prefix script '/home/<USER>/znjy_ws/install/local_setup.bash'
[2.784s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating prefix chain script '/home/<USER>/znjy_ws/install/setup.bash'
[2.791s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating prefix script '/home/<USER>/znjy_ws/install/local_setup.zsh'
[2.793s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating prefix chain script '/home/<USER>/znjy_ws/install/setup.zsh'
