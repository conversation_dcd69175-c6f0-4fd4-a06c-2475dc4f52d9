[0.037s] Invoking command in '/home/<USER>/znjy_ws/build/wy_actions': CMAKE_PREFIX_PATH=/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/znjy_ws/install/user_msgs/lib:/home/<USER>/znjy_ws/install/wy_actions/lib:/home/<USER>/znjy_ws/install/usb_cam/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib:/usr/local/lib:/usr/local/cuda-12.6/lib64 /usr/bin/cmake --build /home/<USER>/znjy_ws/build/wy_actions -- -j6 -l6
[0.166s] [  9%] Built target wy_actions__cpp
[0.167s] [ 12%] Built target wy_actions__rosidl_generator_c
[0.216s] [ 12%] Built target ament_cmake_python_copy_wy_actions
[0.219s] [ 22%] Built target wy_actions__rosidl_typesupport_fastrtps_c
[0.222s] [ 32%] Built target wy_actions__rosidl_typesupport_introspection_c
[0.236s] [ 41%] Built target wy_actions__rosidl_typesupport_c
[0.239s] [ 51%] Built target wy_actions__rosidl_typesupport_cpp
[0.242s] [ 61%] Built target wy_actions__rosidl_typesupport_fastrtps_cpp
[0.281s] [ 70%] Built target wy_actions__rosidl_typesupport_introspection_cpp
[0.321s] [ 70%] Built target wy_actions
[0.362s] [ 74%] Built target wy_actions__py
[0.411s] [ 80%] Built target wy_actions__rosidl_generator_py
[0.458s] [ 96%] Built target wy_actions__rosidl_typesupport_fastrtps_c__pyext
[0.458s] [100%] Built target wy_actions__rosidl_typesupport_c__pyext
[0.459s] [100%] Built target wy_actions__rosidl_typesupport_introspection_c__pyext
[0.947s] running egg_info
[0.950s] writing wy_actions.egg-info/PKG-INFO
[0.951s] writing dependency_links to wy_actions.egg-info/dependency_links.txt
[0.951s] writing top-level names to wy_actions.egg-info/top_level.txt
[0.958s] reading manifest file 'wy_actions.egg-info/SOURCES.txt'
[0.959s] writing manifest file 'wy_actions.egg-info/SOURCES.txt'
[1.057s] [100%] Built target ament_cmake_python_build_wy_actions_egg
[1.084s] Invoked command in '/home/<USER>/znjy_ws/build/wy_actions' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/znjy_ws/install/user_msgs/lib:/home/<USER>/znjy_ws/install/wy_actions/lib:/home/<USER>/znjy_ws/install/usb_cam/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib:/usr/local/lib:/usr/local/cuda-12.6/lib64 /usr/bin/cmake --build /home/<USER>/znjy_ws/build/wy_actions -- -j6 -l6
[1.112s] Invoking command in '/home/<USER>/znjy_ws/build/wy_actions': CMAKE_PREFIX_PATH=/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/znjy_ws/install/user_msgs/lib:/home/<USER>/znjy_ws/install/wy_actions/lib:/home/<USER>/znjy_ws/install/usb_cam/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib:/usr/local/lib:/usr/local/cuda-12.6/lib64 /usr/bin/cmake --install /home/<USER>/znjy_ws/build/wy_actions
[1.154s] -- Install configuration: ""
[1.169s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/ament_index/resource_index/rosidl_interfaces/wy_actions
[1.169s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions
[1.170s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action
[1.170s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail
[1.171s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__struct.h
[1.171s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__functions.c
[1.172s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__functions.h
[1.172s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__type_support.h
[1.172s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/main_task.h
[1.173s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg
[1.173s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg/rosidl_generator_c__visibility_control.h
[1.174s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/library_path.sh
[1.174s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/library_path.dsv
[1.174s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_generator_c.so
[1.175s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions
[1.175s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action
[1.175s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail
[1.176s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__rosidl_typesupport_fastrtps_c.h
[1.176s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg
[1.176s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg/rosidl_typesupport_fastrtps_c__visibility_control.h
[1.177s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_fastrtps_c.so
[1.177s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions
[1.177s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action
[1.177s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail
[1.177s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__rosidl_typesupport_introspection_c.h
[1.178s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__type_support.c
[1.178s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg
[1.178s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg/rosidl_typesupport_introspection_c__visibility_control.h
[1.178s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_introspection_c.so
[1.178s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_c.so
[1.178s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions
[1.178s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action
[1.179s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail
[1.179s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__traits.hpp
[1.179s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__builder.hpp
[1.180s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__struct.hpp
[1.180s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__type_support.hpp
[1.180s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/main_task.hpp
[1.180s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg
[1.181s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg/rosidl_generator_cpp__visibility_control.hpp
[1.181s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions
[1.181s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action
[1.181s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail
[1.182s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__rosidl_typesupport_fastrtps_cpp.hpp
[1.182s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/dds_fastrtps
[1.182s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg
[1.182s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h
[1.182s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_fastrtps_cpp.so
[1.182s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions
[1.183s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action
[1.183s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail
[1.183s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__type_support.cpp
[1.183s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/include/wy_actions/wy_actions/action/detail/main_task__rosidl_typesupport_introspection_cpp.hpp
[1.183s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_introspection_cpp.so
[1.183s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_typesupport_cpp.so
[1.183s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/pythonpath.sh
[1.183s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/pythonpath.dsv
[1.184s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions-0.0.0-py3.10.egg-info
[1.184s] -- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions-0.0.0-py3.10.egg-info/dependency_links.txt
[1.184s] -- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions-0.0.0-py3.10.egg-info/top_level.txt
[1.184s] -- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions-0.0.0-py3.10.egg-info/SOURCES.txt
[1.185s] -- Installing: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions-0.0.0-py3.10.egg-info/PKG-INFO
[1.185s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions
[1.185s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/action
[1.186s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/action/__init__.py
[1.186s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/action/_main_task.py
[1.186s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/action/_main_task_s.c
[1.186s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/_wy_actions_s.ep.rosidl_typesupport_introspection_c.c
[1.186s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/__init__.py
[1.187s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_fastrtps_c.cpython-310-aarch64-linux-gnu.so
[1.187s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/_wy_actions_s.ep.rosidl_typesupport_c.c
[1.187s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/libwy_actions__rosidl_generator_py.so
[1.187s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/_wy_actions_s.ep.rosidl_typesupport_fastrtps_c.c
[1.187s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_c.cpython-310-aarch64-linux-gnu.so
[1.187s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_introspection_c.cpython-310-aarch64-linux-gnu.so
[1.267s] Listing '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions'...
[1.267s] Listing '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/action'...
[1.267s] Listing '/home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/drive'...
[1.277s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_fastrtps_c.cpython-310-aarch64-linux-gnu.so
[1.278s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_introspection_c.cpython-310-aarch64-linux-gnu.so
[1.279s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/local/lib/python3.10/dist-packages/wy_actions/wy_actions_s__rosidl_typesupport_c.cpython-310-aarch64-linux-gnu.so
[1.280s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/lib/libwy_actions__rosidl_generator_py.so
[1.281s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/action/MainTask.idl
[1.281s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/action/MainTask.action
[1.281s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/ament_index/resource_index/package_run_dependencies/wy_actions
[1.282s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/ament_index/resource_index/parent_prefix_path/wy_actions
[1.282s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/ament_prefix_path.sh
[1.282s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/ament_prefix_path.dsv
[1.282s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/path.sh
[1.282s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/environment/path.dsv
[1.283s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/local_setup.bash
[1.283s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/local_setup.sh
[1.283s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/local_setup.zsh
[1.283s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/local_setup.dsv
[1.283s] -- Installing: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.dsv
[1.284s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/ament_index/resource_index/packages/wy_actions
[1.285s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_cExport.cmake
[1.285s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_cExport-noconfig.cmake
[1.285s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_typesupport_fastrtps_cExport.cmake
[1.285s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_typesupport_fastrtps_cExport-noconfig.cmake
[1.286s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_introspection_cExport.cmake
[1.286s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_introspection_cExport-noconfig.cmake
[1.287s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_cExport.cmake
[1.287s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_cExport-noconfig.cmake
[1.287s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_cppExport.cmake
[1.287s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_typesupport_fastrtps_cppExport.cmake
[1.288s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake
[1.288s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_introspection_cppExport.cmake
[1.288s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_introspection_cppExport-noconfig.cmake
[1.288s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_cppExport.cmake
[1.289s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actions__rosidl_typesupport_cppExport-noconfig.cmake
[1.289s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_pyExport.cmake
[1.289s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/export_wy_actions__rosidl_generator_pyExport-noconfig.cmake
[1.290s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/rosidl_cmake-extras.cmake
[1.290s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/ament_cmake_export_dependencies-extras.cmake
[1.290s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/ament_cmake_export_include_directories-extras.cmake
[1.290s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/ament_cmake_export_libraries-extras.cmake
[1.291s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/ament_cmake_export_targets-extras.cmake
[1.291s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake
[1.291s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake
[1.291s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actionsConfig.cmake
[1.292s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/cmake/wy_actionsConfig-version.cmake
[1.292s] -- Up-to-date: /home/<USER>/znjy_ws/install/wy_actions/share/wy_actions/package.xml
[1.299s] Invoked command in '/home/<USER>/znjy_ws/build/wy_actions' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/znjy_ws/install/user_msgs:/home/<USER>/znjy_ws/install/wy_actions:/home/<USER>/znjy_ws/install/usb_cam:/home/<USER>/ws00_helloworld/install/pkg01_helloworld_cpp:/home/<USER>/znjy_ws/install/yahboomcar_description:/home/<USER>/znjy_ws/install/yahboomcar_ctrl:/home/<USER>/znjy_ws/install/yahboomcar_bringup:/home/<USER>/znjy_ws/install/detection:/home/<USER>/znjy_ws/install/my_servo_control:/home/<USER>/ws00_helloworld/install/pkg02_helloworld_py:/home/<USER>/yahboom_ws/install/camera:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/znjy_ws/install/user_msgs/lib:/home/<USER>/znjy_ws/install/wy_actions/lib:/home/<USER>/znjy_ws/install/usb_cam/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib:/usr/local/lib:/usr/local/cuda-12.6/lib64 /usr/bin/cmake --install /home/<USER>/znjy_ws/build/wy_actions
