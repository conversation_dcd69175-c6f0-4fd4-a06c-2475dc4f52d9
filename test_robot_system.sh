#!/bin/bash

echo "🤖 智能机器人系统完整测试脚本"
echo "=================================="

# 设置正确的环境
export ROS_DOMAIN_ID=0
cd ~/znjy_ws

# 1. 环境检查
echo "📋 1. 环境检查..."
echo "ROS_DOMAIN_ID: $ROS_DOMAIN_ID"
echo "工作目录: $(pwd)"

# 检查串口设备
if [ -e "/dev/ttyUSB1" ]; then
    echo "✅ 串口设备 /dev/ttyUSB1 存在"
    ls -la /dev/ttyUSB1
else
    echo "❌ 串口设备 /dev/ttyUSB1 不存在"
    echo "可用串口设备:"
    ls -la /dev/ttyUSB* /dev/ttyACM* 2>/dev/null || echo "没有找到串口设备"
fi

# 检查符号链接
if [ -L "/dev/myserial" ]; then
    echo "✅ 符号链接 /dev/myserial 存在: $(readlink /dev/myserial)"
else
    echo "❌ 符号链接 /dev/myserial 不存在"
fi

# 2. 清理旧进程
echo ""
echo "🧹 2. 清理旧进程..."
pkill -f "blue_server|red_server|yolo_ros" 2>/dev/null || true
sleep 2

# 检查串口是否被占用
if lsof /dev/ttyUSB1 2>/dev/null; then
    echo "⚠️  串口仍被占用，强制清理..."
    pkill -9 -f "python3.*wy_actions" 2>/dev/null || true
    sleep 3
fi

# 3. 重启ROS2守护进程
echo ""
echo "🔄 3. 重启ROS2守护进程..."
source /opt/ros/humble/setup.bash
source install/setup.bash
ros2 daemon stop > /dev/null 2>&1
ros2 daemon start > /dev/null 2>&1

# 4. 检查包是否可用
echo ""
echo "📦 4. 检查ROS2包..."
if ros2 pkg list | grep -q wy_actions; then
    echo "✅ wy_actions包可用"
else
    echo "❌ wy_actions包不可用"
    exit 1
fi

if ros2 pkg list | grep -q detection; then
    echo "✅ detection包可用"
else
    echo "❌ detection包不可用"
    exit 1
fi

# 5. 启动系统组件
echo ""
echo "🚀 5. 启动系统组件..."

# 启动YOLO检测节点
echo "启动YOLO检测节点..."
gnome-terminal --title="YOLO Detection" -- bash -c "
    export ROS_DOMAIN_ID=0
    cd ~/znjy_ws
    source /opt/ros/humble/setup.bash
    source install/setup.bash
    ros2 run detection yolo_ros
    exec bash
" &
sleep 5

# 启动blue_server
echo "启动blue_server..."
gnome-terminal --title="Blue Server" -- bash -c "
    export ROS_DOMAIN_ID=0
    cd ~/znjy_ws
    source /opt/ros/humble/setup.bash
    source install/setup.bash
    ros2 run wy_actions blue_server
    exec bash
" &
sleep 3

# 6. 系统状态检查
echo ""
echo "🔍 6. 系统状态检查..."
echo "ROS2节点列表:"
ros2 node list

echo ""
echo "ROS2话题列表:"
ros2 topic list | grep -E "(yolo|cmd_vel)"

echo ""
echo "动作服务器列表:"
ros2 action list

# 7. 测试客户端连接
echo ""
echo "🧪 7. 测试客户端连接..."
echo "启动blue_client进行测试..."
gnome-terminal --title="Blue Client Test" -- bash -c "
    export ROS_DOMAIN_ID=0
    cd ~/znjy_ws
    source /opt/ros/humble/setup.bash
    source install/setup.bash
    echo '准备启动blue_client，按Enter继续...'
    read
    timeout 15 ros2 run wy_actions blue_client
    echo '测试完成，按Enter关闭...'
    read
" &

echo ""
echo "✅ 系统启动完成！"
echo ""
echo "📋 下一步操作："
echo "1. 检查各个终端窗口的输出"
echo "2. 确认YOLO检测节点正在输出检测数据"
echo "3. 确认blue_server显示'Rosmaster Serial Opened!'"
echo "4. 在blue_client测试窗口中按Enter开始测试"
echo ""
echo "🔧 如果小车仍不移动，请检查："
echo "- 摄像头前是否有可识别的目标物体"
echo "- 串口连接是否正常"
echo "- 下位机是否正常供电"
echo ""
echo "📞 获取更多帮助："
echo "- 查看各终端的详细输出"
echo "- 检查 /cmd_vel 话题是否有数据: ros2 topic echo /cmd_vel"
