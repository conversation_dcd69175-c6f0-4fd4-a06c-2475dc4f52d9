#!/bin/bash

echo "启动蓝队机器人系统..."

# 设置正确的ROS环境
export ROS_DOMAIN_ID=0
cd ~/znjy_ws
source /opt/ros/humble/setup.bash
source install/setup.bash

# 重启ROS2守护进程以确保环境正确
ros2 daemon stop > /dev/null 2>&1
ros2 daemon start > /dev/null 2>&1

# 检查是否已经有进程在运行
if pgrep -f "usb_cam_node_exe" > /dev/null; then
    echo "摄像头节点已在运行"
else
    echo "启动摄像头节点..."
    gnome-terminal -- bash -c "cd ~/znjy_ws && source install/setup.bash && ros2 run usb_cam usb_cam_node_exe; exec bash"
    sleep 2
fi

if pgrep -f "yolo_ros" > /dev/null; then
    echo "YOLO检测节点已在运行"
else
    echo "启动YOLO检测节点..."
    gnome-terminal -- bash -c "cd ~/znjy_ws && source install/setup.bash && ros2 run detection yolo_ros; exec bash"
    sleep 3
fi

if pgrep -f "blue_server" > /dev/null; then
    echo "蓝队服务器已在运行"
else
    echo "启动蓝队动作服务器..."
    gnome-terminal -- bash -c "cd ~/znjy_ws && source install/setup.bash && ./install/wy_actions/lib/wy_actions/blue_server; exec bash"
    sleep 2
fi

echo "等待所有节点启动完成..."
sleep 5

echo "检查系统状态..."
echo "ROS2话题列表："
ros2 topic list | grep -E "(yolo|cmd_vel|usb_cam)"

echo ""
echo "动作服务器列表："
ros2 action list

echo ""
echo "系统启动完成！现在可以运行客户端："
echo "cd ~/znjy_ws && source install/setup.bash && ./install/wy_actions/lib/wy_actions/blue_client"
