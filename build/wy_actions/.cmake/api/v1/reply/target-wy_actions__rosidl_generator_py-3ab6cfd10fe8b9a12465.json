{"artifacts": [{"path": "rosidl_generator_py/wy_actions/libwy_actions__rosidl_generator_py.so"}], "backtrace": 5, "backtraceGraph": {"commands": ["add_library", "include", "ament_execute_extensions", "rosidl_generate_interfaces", "install", "target_link_libraries", "add_dependencies", "set_target_properties", "set_lib_properties", "add_definitions", "find_package", "target_include_directories"], "files": ["/opt/ros/humble/share/rosidl_generator_py/cmake/rosidl_generator_py_generate_interfaces.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_ros-extras.cmake", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_rosConfig.cmake", "/opt/ros/humble/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_c_generate_interfaces.cmake"], "nodes": [{"file": 3}, {"command": 3, "file": 3, "line": 18, "parent": 0}, {"command": 2, "file": 2, "line": 286, "parent": 1}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 0, "parent": 3}, {"command": 0, "file": 0, "line": 166, "parent": 4}, {"command": 4, "file": 0, "line": 302, "parent": 4}, {"command": 5, "file": 0, "line": 175, "parent": 4}, {"command": 5, "file": 0, "line": 213, "parent": 4}, {"command": 5, "file": 0, "line": 291, "parent": 4}, {"command": 5, "file": 0, "line": 291, "parent": 4}, {"command": 5, "file": 0, "line": 291, "parent": 4}, {"command": 5, "file": 0, "line": 167, "parent": 4}, {"command": 5, "file": 0, "line": 291, "parent": 4}, {"command": 5, "file": 0, "line": 291, "parent": 4}, {"command": 6, "file": 0, "line": 169, "parent": 4}, {"command": 8, "file": 0, "line": 294, "parent": 4}, {"command": 7, "file": 0, "line": 156, "parent": 16}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 6, "parent": 18}, {"command": 10, "file": 6, "line": 21, "parent": 19}, {"file": 5, "parent": 20}, {"command": 1, "file": 5, "line": 41, "parent": 21}, {"file": 4, "parent": 22}, {"command": 9, "file": 4, "line": 25, "parent": 23}, {"command": 11, "file": 0, "line": 179, "parent": 4}, {"command": 11, "file": 0, "line": 209, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-fPIC"}, {"backtrace": 17, "fragment": "-Wall"}, {"backtrace": 17, "fragment": "-Wextra"}], "defines": [{"backtrace": 12, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}, {"backtrace": 24, "define": "ROS_PACKAGE_NAME=\"wy_actions\""}, {"define": "wy_actions__rosidl_generator_py_EXPORTS"}], "includes": [{"backtrace": 25, "path": "/home/<USER>/znjy_ws/build/wy_actions/rosidl_generator_c"}, {"backtrace": 25, "path": "/home/<USER>/znjy_ws/build/wy_actions/rosidl_generator_py"}, {"backtrace": 25, "path": "/usr/include/python3.10"}, {"backtrace": 26, "path": "/home/<USER>/.local/lib/python3.10/site-packages/numpy/core/include"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/humble/include/action_msgs"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/humble/include/unique_identifier_msgs"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/humble/include/geometry_msgs"}], "language": "C", "sourceIndexes": [0]}], "dependencies": [{"backtrace": 12, "id": "wy_actions__rosidl_generator_c::@6890427a1f51a3e7e1df"}, {"backtrace": 15, "id": "wy_actions__rosidl_typesupport_c::@6890427a1f51a3e7e1df"}, {"backtrace": 15, "id": "wy_actions__py::@1a1c43757699ccd64682"}], "id": "wy_actions__rosidl_generator_py::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 6, "path": "lib"}, {"backtrace": 6, "path": "lib"}], "prefix": {"path": "/home/<USER>/znjy_ws/install/wy_actions"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"fragment": "-Wl,-r<PERSON>,/home/<USER>/znjy_ws/build/wy_actions:/opt/ros/humble/lib:", "role": "libraries"}, {"backtrace": 7, "fragment": "/usr/lib/aarch64-linux-gnu/libpython3.10.so", "role": "libraries"}, {"backtrace": 8, "fragment": "libwy_actions__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 12, "fragment": "libwy_actions__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/usr/lib/aarch64-linux-gnu/libpython3.10.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"fragment": "-ldl", "role": "libraries"}, {"fragment": "-Wl,-rpath-link,/opt/ros/humble/lib", "role": "libraries"}], "language": "C"}, "name": "wy_actions__rosidl_generator_py", "nameOnDisk": "libwy_actions__rosidl_generator_py.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/znjy_ws/build/wy_actions/rosidl_generator_py/wy_actions/action/_main_task_s.c", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}